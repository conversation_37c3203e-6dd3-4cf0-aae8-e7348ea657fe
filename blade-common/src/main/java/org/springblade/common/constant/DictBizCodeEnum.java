package org.springblade.common.constant;

import lombok.Getter;

@Getter
public enum DictBizCodeEnum {
	/**
	 * 字典
	 */
    WF_SCHEDULE("wf_schedule","代理商工作流节点"),
    AGENT_PROJECT_TYPE("agent_project_type", "项目类型"),
	INFRASTRUCTURE("infrastructure", "电力基础设施"),
	INFRASTRUCTURE_CONNECTION_TYPE("infrastructure_connection_type", "基础设施连接类型"),
	ROOF_TYPE("roof_type", "屋顶类型"),
	MOUNTING_STRUCTURE("mounting_structure", "安装结构"),
	PANEL_TYPE("panel_type", "板类型"),
	PANEL_ORIENTATION("panel_orientation", "面板定向"),
	INVERTER_TYPE("inverter_type", "逆变器类型"),
	AGENT_EHS_TOOLS_MACHINERY_TYPE("agent_ehs_tools_machinery_type","工具设备类型"),
	AGENT_ITEM_BASE_PACKAGE("agent_item_base_package","代理商物料基础包"),
	AGENT_SKU_DEVICE_TYPE("sku_device_type","物料类型"),
	AGENT_ITEM_FINAL_PRICE_RULE("agent_item_final_price_rule","最终价格计算规则"),
	AGENT_EXCLUDE_ITEM("exclude_item","需要排除的item编码"),
	AGENT_SEPARATE_ITEM_PRICE("separate_item_price","需要单独计算价格物料"),
	SKU_QUOTE_DEVICE_ITEMS_A("sku_quote_device_items_a","报价单设备清单A"),
	SKU_QUOTE_DEVICE_ITEMS_B("sku_quote_device_items_b","报价单设备清单B"),
	SYSTEM_DEFAULT_CONFIGURATION("system_default_configuration","系统默认参数配置"),
	CLIENT_DEVICE_MODE_TYPE("device_mode_type","设备类型"),
	DEVICE_ALARM_MAPPING_CONFIG("device_alarm_mapping_config","告警等级&推送信息配置"),
	DEVICE_TYPE("device_type","设备类型"),
	DEVICE_MODE_TYPE("device_mode_type","逆变器设备类型"),
	BATTERY_MODE_TYPE("battery_mode_type","电池设备类型")	,
	CONTROL_FROM_TO_GRID_SWITCH("control_from_to_grid_switch","fromToGrid控制开关"),
	APP_REMARK_DEFINITION_ADMIN("app_remark_definition_admin","app上传图片备注定义管理员");
    private final String dictCode;
    private final String remark;
    DictBizCodeEnum(String dictCode, String remark) {
        this.dictCode = dictCode;
        this.remark = remark;
    }
}
