/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.feign;

import com.alibaba.fastjson.JSONObject;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.*;
import org.springblade.system.vo.ChangeUserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = AppConstant.APPLICATION_SYSTEM_NAME,
	fallback = ISysClientFallback.class
)
public interface ISysClient {

	String API_PREFIX = "/client";
	String MENU = API_PREFIX + "/menu";
	String DEPT = API_PREFIX + "/dept";
	String DEPT_IDS = API_PREFIX + "/dept-ids";
	String DEPT_IDS_FUZZY = API_PREFIX + "/dept-ids-fuzzy";
	String DEPT_NAME = API_PREFIX + "/dept-name";
	String DEPT_NAMES = API_PREFIX + "/dept-names";
	String DEPT_CHILD = API_PREFIX + "/dept-child";
	String DEPT_ADD = API_PREFIX + "dept-add";
	String DEPT_REMOVE = API_PREFIX + "dept-remove";
	String POST = API_PREFIX + "/post";
	String POST_IDS = API_PREFIX + "/post-ids";
	String POST_IDS_FUZZY = API_PREFIX + "/post-ids-fuzzy";
	String POST_NAME = API_PREFIX + "/post-name";
	String POST_NAMES = API_PREFIX + "/post-names";
	String ROLE = API_PREFIX + "/role";
	String ROLE_IDS = API_PREFIX + "/role-ids";
	String ROLE_NAME = API_PREFIX + "/role-name";
	String ROLE_NAMES = API_PREFIX + "/role-names";
	String ROLE_ALIAS = API_PREFIX + "/role-alias";
	String ROLE_ALIASES = API_PREFIX + "/role-aliases";
	String TENANT = API_PREFIX + "/tenant";
	String TENANT_ID = API_PREFIX + "/tenant-id";
	String TENANT_PACKAGE = API_PREFIX + "/tenant-package";
	String PARAM = API_PREFIX + "/param";
	String PARAM_VALUE = API_PREFIX + "/param-value";
	String REGION = API_PREFIX + "/region";

	String REGION_LIST = API_PREFIX + "/regionList";

	String REDIS_SEQUENCE = "/sequence";

	String REDIS_SEQUENCE_UNIQUE_ID = REDIS_SEQUENCE + "/getOne";

	String REDIS_SEQUENCE_CLEAR = REDIS_SEQUENCE + "/clear";

	String ROLE_ID_BY_CODE = API_PREFIX + "/getRoleIdByCode";

	String ROLE_LIST = API_PREFIX + "/role-list";
	String GET_ROLE_IGNORE_TENANT = API_PREFIX + "/getRoleIgnoreTenant";
	String ROLE_INFO_BY_USER = API_PREFIX + "/findUserRoleInfoByUserId";

	String ALARM_CONFIG_LIST = API_PREFIX + "/alarmConfigList";

	String USER_FIND = API_PREFIX + "/userFind";

	String FIND_USER = API_PREFIX + "/findUser";

	String FIND_USER_TEST = API_PREFIX + "/findUserTest";

	String USER_FIND_TEST = API_PREFIX + "/userFindTest";

	/**
	 * 获取菜单
	 *
	 * @param id 主键
	 * @return Menu
	 */
	@GetMapping(MENU)
	R<Menu> getMenu(@RequestParam("id") Long id);

	/**
	 * 获取部门
	 *
	 * @param id 主键
	 * @return Dept
	 */
	@GetMapping(DEPT)
	R<Dept> getDept(@RequestParam("id") Long id);

	/**
	 * 获取部门id
	 *
	 * @param tenantId  租户id
	 * @param deptNames 部门名
	 * @return 部门id
	 */
	@GetMapping(DEPT_IDS)
	R<String> getDeptIds(@RequestParam("tenantId") String tenantId, @RequestParam("deptNames") String deptNames);

	/**
	 * 获取部门id
	 *
	 * @param tenantId  租户id
	 * @param deptNames 部门名
	 * @return 部门id
	 */
	@GetMapping(DEPT_IDS_FUZZY)
	R<String> getDeptIdsByFuzzy(@RequestParam("tenantId") String tenantId, @RequestParam("deptNames") String deptNames);

	/**
	 * 获取部门名
	 *
	 * @param id 主键
	 * @return 部门名
	 */
	@GetMapping(DEPT_NAME)
	R<String> getDeptName(@RequestParam("id") Long id);

	/**
	 * 获取部门名
	 *
	 * @param deptIds 主键
	 * @return
	 */
	@GetMapping(DEPT_NAMES)
	R<List<String>> getDeptNames(@RequestParam("deptIds") String deptIds);

	/**
	 * 获取子部门ID
	 *
	 * @param deptId
	 * @return
	 */
	@GetMapping(DEPT_CHILD)
	R<List<Dept>> getDeptChild(@RequestParam("deptId") Long deptId);

	/**
	 * 新增部门
	 *
	 * @param dept 入参
	 * @return R<Long>
	 * <AUTHOR>
	 * @since 2023/11/10 14:04
	 **/
	@PostMapping(DEPT_ADD)
	R<Boolean> submit(@RequestBody Dept dept);

	/**
	 * 删除部门信息
	 *
	 * @param ids 入参
	 * @return R<Boolean>
	 * <AUTHOR>
	 * @since 2024/1/3 19:43
	 **/
	@GetMapping(DEPT_REMOVE)
	R<Boolean> removeDept(@RequestParam("ids") String ids);

	/**
	 * 获取岗位
	 *
	 * @param id 主键
	 * @return Post
	 */
	@GetMapping(POST)
	R<Post> getPost(@RequestParam("id") Long id);

	/**
	 * 获取岗位id
	 *
	 * @param tenantId  租户id
	 * @param postNames 岗位名
	 * @return 岗位id
	 */
	@GetMapping(POST_IDS)
	R<String> getPostIds(@RequestParam("tenantId") String tenantId, @RequestParam("postNames") String postNames);

	/**
	 * 获取岗位id
	 *
	 * @param tenantId  租户id
	 * @param postNames 岗位名
	 * @return 岗位id
	 */
	@GetMapping(POST_IDS_FUZZY)
	R<String> getPostIdsByFuzzy(@RequestParam("tenantId") String tenantId, @RequestParam("postNames") String postNames);

	/**
	 * 获取岗位名
	 *
	 * @param id 主键
	 * @return 岗位名
	 */
	@GetMapping(POST_NAME)
	R<String> getPostName(@RequestParam("id") Long id);

	/**
	 * 获取岗位名
	 *
	 * @param postIds 主键
	 * @return
	 */
	@GetMapping(POST_NAMES)
	R<List<String>> getPostNames(@RequestParam("postIds") String postIds);

	/**
	 * 获取角色
	 *
	 * @param id 主键
	 * @return Role
	 */
	@GetMapping(ROLE)
	R<Role> getRole(@RequestParam("id") Long id);

	/**
	 * 获取角色id
	 *
	 * @param tenantId  租户id
	 * @param roleNames 角色名
	 * @return 角色id
	 */
	@GetMapping(ROLE_IDS)
	R<String> getRoleIds(@RequestParam("tenantId") String tenantId, @RequestParam("roleNames") String roleNames);

	/**
	 * 获取角色名
	 *
	 * @param id 主键
	 * @return 角色名
	 */
	@GetMapping(ROLE_NAME)
	R<String> getRoleName(@RequestParam("id") Long id);

	/**
	 * 获取角色别名
	 *
	 * @param id 主键
	 * @return 角色别名
	 */
	@GetMapping(ROLE_ALIAS)
	R<String> getRoleAlias(@RequestParam("id") Long id);

	/**
	 * 获取角色名
	 *
	 * @param roleIds 主键
	 * @return
	 */
	@GetMapping(ROLE_NAMES)
	R<List<String>> getRoleNames(@RequestParam("roleIds") String roleIds);

	/**
	 * 获取角色别名
	 *
	 * @param roleIds 主键
	 * @return 角色别名
	 */
	@GetMapping(ROLE_ALIASES)
	R<List<String>> getRoleAliases(@RequestParam("roleIds") String roleIds);


	/**
	 * 根据角色编码查询角色信息
	 *
	 * @param roleCode 角色编码
	 * @return 角色信息
	 */
	@GetMapping(ROLE_ID_BY_CODE)
	R<Role> getRoleInfoByCode(@RequestParam("roleCode") String roleCode);


	/**
	 * 获取租户
	 *
	 * @param id 主键
	 * @return Tenant
	 */
	@GetMapping(TENANT)
	R<Tenant> getTenant(@RequestParam("id") Long id);

	/**
	 * 获取租户
	 *
	 * @param tenantId 租户id
	 * @return Tenant
	 */
	@GetMapping(TENANT_ID)
	R<Tenant> getTenant(@RequestParam("tenantId") String tenantId);

	/**
	 * 获取租户产品包
	 *
	 * @param tenantId 租户id
	 * @return Tenant
	 */
	@GetMapping(TENANT_PACKAGE)
	R<TenantPackage> getTenantPackage(@RequestParam("tenantId") String tenantId);

	/**
	 * 获取参数
	 *
	 * @param id 主键
	 * @return Param
	 */
	@GetMapping(PARAM)
	R<Param> getParam(@RequestParam("id") Long id);

	/**
	 * 获取参数配置
	 *
	 * @param paramKey 参数key
	 * @return String
	 */
	@GetMapping(PARAM_VALUE)
	R<String> getParamValue(@RequestParam("paramKey") String paramKey);

	/**
	 * 获取行政区划
	 *
	 * @param code 主键
	 * @return Region
	 */
	@GetMapping(REGION)
	R<Region> getRegion(@RequestParam("code") String code);

	@PostMapping(REGION_LIST)
	R<List<Region>> getRegionList(@RequestBody List<String> codeList);

	@GetMapping(REDIS_SEQUENCE_UNIQUE_ID)
	R<String> getRedisUniqueId(@RequestParam RedisSeqEnums redisSeqEnums);

	@GetMapping(REDIS_SEQUENCE_CLEAR)
	R clearRedisSequence();

	@GetMapping(ROLE_INFO_BY_USER)
	R<List<Role>> findUserRoleInfoByUserId(@RequestParam("userId") String userId);

	@GetMapping(ROLE_LIST)
	R<List<Role>> getRoleList(@RequestParam("roleIds") String roleIds);

	@GetMapping(GET_ROLE_IGNORE_TENANT)
	R<List<Role>> getRoleIgnoreTenant(@RequestParam("roleCodeList") List<String> roleCodeList, @RequestParam("tenantId") String tenantId);

	@GetMapping(ALARM_CONFIG_LIST)
	R<JSONObject> alarmConfigList(@RequestParam("language") String language,
								  @RequestParam("roleNames")String roleNames, @RequestParam("deptIds")String deptIds);

	@GetMapping(USER_FIND)
	R<ChangeUserVO> userFind(@RequestParam("areaCode") String areaCode, @RequestParam("phoneNumber") String phoneNumber,
							 @RequestParam("email") String email, @RequestParam("password") String password);
}
