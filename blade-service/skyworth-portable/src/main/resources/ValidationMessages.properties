system.error=系统错误
portable.device.exit.factory.deviceSerialNumber.notNull=设备SN不能为空
parameter.notEmpty=参数不能为空
portable.device.exist.multiple.records=当前用户下该设备SN(%s)记录重复
portable.device.not.duplicate.association=设备已被该用户关联过，不能重复关联
portable.device.exit.factory.deviceType.notNull=设备型号不能为空
portable.device.exit.factory.company.notNull=厂家不能为空
portable.device.exit.factory.qualityGuaranteeYear.notNull=质保年限不能为空
portable.device.exit.factory.exitFactoryDate.notNull=出厂日期不能为空
portable.device.exit.factory.batteryRatedCapacity.notNull=电池额定容量不能为空
portable.device.exit.factory.batteryRatedAcDischargeCapacity.notNull=电池额定交流放电总功率不能为空
portable.device.exit.factory.batteryRatedDischargeCapacity.notNull=电池额定直流放电总功率不能为空
portable.device.exit.factory.batteryRatedDcDischargeCapacity.notNull=电池额定充电总功率不能为空
portable.device.exit.factory.batteryRatedChargeCapacity.notNull=电池额定充电总功率不能为空
portable.device.exit.factory.batteryRatedAndersonChargeCapacity.notNull=电池额定安德森接口充电总功率不能为空
portable.device.exit.factory.bluetoothProtocolVersion.notNull=蓝牙协议版本不能为空
portable.device.exit.factory.motherboardHardwareVersion.notNull=主板硬件版本不能为空
portable.device.exit.factory.deviceSerialNumber.is.exist=设备SN已存在
portable.device.exit.factory.deviceSerialNumber.is.used=设备SN: %s 已被使用，不能删除
portable.device.maintain.exit.factoryInfo=请维护该设备出厂信息
portable.device.exit.factory.had.save.quality.guarantee=此SN产品保修已存在，不能重复提交
portable.device.exit.factory.buyChannel.notNull=销售渠道不能为空
portable.device.exit.factory.buyDate.notNull=购买日期不能为空
portable.device.exit.factory.buyOrderNumber.notNull=订单编号不能为空
portable.device.exit.factory.buyCertificateImgBizKey.notNull=购买凭证不能为空
portable.file.created.directory.is.failed=创建目录失败
portable.file.delete.is.failed=删除文件失败
portable.device.exit.factory.import.deviceSerialNumber.is.exist=设备SN：%s 已存在
portable.device.exit.factory.modify.import.deviceSerialNumber.is.not.exist=设备SN：%s 不存在，请使用新增导入
portable.device.exit.factory.import.dict.is.not.exist=设备类型、公司业务字典不存在：portable_device_type,portable_company
portable.device.exit.factory.import.dict.name.is.error=设备类型、公司业务不正确： %s
portable.device.exit.factory.not.exists=设备序列号未维护
