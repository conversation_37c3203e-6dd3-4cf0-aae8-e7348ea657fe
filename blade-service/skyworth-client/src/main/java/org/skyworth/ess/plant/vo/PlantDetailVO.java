package org.skyworth.ess.plant.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.springblade.core.tenant.mp.SkyWorthEntity;

import java.util.List;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024年5月14日 19:13:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlantDetailVO extends SkyWorthEntity {

	private String plantName;

	private String detailAddress;

	private String timeZone;

	@ApiModelProperty(value = "站点状态，由站点下不同设备（逆变器、电池等）汇总，如果其中一个异常站点则异常")
	private String plantStatus;

	/**
	 * 日发电量 pv
	 */
	private String powerGeneration;
	/**
	 * 日储电量  es
	 */
	private String loadCapacity;
	/**
	 * 逆变器sn
	 */
	private String deviceSerialNumber;
	/**
	 * 地址
	 */
	private String address;
	/**
	 * 0 开机
	 */
	private String inverterControl;

	/**
	 * 该逆变器是否开过机：0是 1否
	 */
	private Integer startupByBackstage;
	/**
	 * 该逆变器是否配网成功：0是 1否
	 */
	private Integer inverterConfigureNetwork;

	/**
	 * 运维团队信息
	 */
	private AgentCompanyVO companyInfo;

	/**
	 * 运维人员信息
	 */
	private AgentUserVo agentUserInfo;

	/**
	 * 国家名称
	 */
	private String countryName;
	/**
	 * 一级行政区域名称
	 */
	private String provinceName;
	/**
	 * 二级行政区域名称
	 */
	private String cityName;
	/**
	 * 三级行政区域名称
	 */
	private String countyName;

	@ApiModelProperty(value = "国家")
	private String countryCode;
	/**
	 * 省
	 */
	@ApiModelProperty(value = "省")
	private String provinceCode;
	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String cityCode;
	/**
	 * 区县
	 */
	@ApiModelProperty(value = "区县")
	private String countyCode;

	// 站点下并机设备信息
	private List<PlantDeviceVO> plantDeviceVOList;
	private String isParallelMode;
}
