package org.skyworth.ess.device.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.plant.entity.PhotovoltaicPlantDeviceEntity;
import org.springblade.core.mp.base.BaseEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description 逆变器安装信息
 * @create-time 2023/9/25 09:30:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "逆变器安装信息对象", description = "逆变器安装信息")
public class InverterDeviceInstallVO extends BaseEntity {

	@ApiModelProperty(name = "安装日期",notes = "")
	private String installDate;

	@ApiModelProperty(name = "安装人员",notes = "")
	private String installPerson;

	@ApiModelProperty(name = "安装团队",notes = "")
	private String installTeam;

	@ApiModelProperty(name = "质保年限",notes = "")
	private String qualityGuaranteeYear;

	@ApiModelProperty(name = "质保开始日期",notes = "")
	private String qualityGuaranteeBeginDate;

	@ApiModelProperty(name = "安装日期",notes = "")
	private String qualityGuaranteeEndDate;

	@ApiModelProperty(name = "mppt信息",notes = "")
	private Map<String, List<String>> mpptInfo;

	@ApiModelProperty(name = "电池信息",notes = "")
	private List<BatteryMapDeviceEntity> batteryInfo;

	@ApiModelProperty(name = "逆变器种类",notes = "")
	@TableField(exist = false)
	private String inverterKind;
}
