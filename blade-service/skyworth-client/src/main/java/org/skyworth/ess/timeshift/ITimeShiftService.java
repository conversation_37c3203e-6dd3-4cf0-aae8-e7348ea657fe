package org.skyworth.ess.timeshift;

import java.util.List;

public interface ITimeShiftService {
	/**
	 * 通用方法，遍历列表，把时间字段提取出来，再用目标时区去转换时间
	 *
	 * @param list      数组
	 * @param fieldName 字段名称
	 * @param <T>       数组元素的类型
	 * @return 包含字段值的字符串列表
	 */
	<T> void shiftTimeZoneAndDateTime(List<T> list, String fieldName, List<Long> plantIdList);


	void shiftTimeZoneAndDateTimeForEntity(Object entity, String... fieldNames);


	/**
	 * 通用方法，
	 * 并返回原数组
	 *
	 * @param list      数组
	 * @param fieldName 字段名称
	 * @param <T>       数组元素的类型
	 * @return 原数组
	 */
	<T> List<T> getAndReturnList(List<T> list, String fieldName, List<Long> plantIdList);

	/**
	 * 通用方法，
	 * 并返回原数组
	 *
	 * @param list       列表
	 * @param fieldNames 字段名称
	 * @param <T>        列表元素的类型
	 */
	<T> void getWebAndConvertTimeZones(List<T> list, String timeZone, String... fieldNames);
}
