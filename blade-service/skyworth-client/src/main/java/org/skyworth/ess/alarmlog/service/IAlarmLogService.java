/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmlog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageCondition;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageVO;
import org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;

import java.util.List;

/**
 * 异常日志表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public interface IAlarmLogService extends BaseService<AlarmLogEntity> {
	/**
	 * 查询告警信息列表
	 * @param alarmLogPageCondition 查询条件
	 * @param query 入参
	 * @return IPage<AlarmLogEntity>
	 * <AUTHOR>
	 * @since 2024/8/10 15:43
	 **/
	IPage<AlarmLogPageVO> appList(AlarmLogPageCondition alarmLogPageCondition, Query query);

	/**
	 * 查询告警信息列表
	 * @param alarmLogPageCondition 查询条件
	 * @param query 入参
	 * @return IPage<AlarmLogEntity>
	 * <AUTHOR>
	 * @since 2024/8/10 15:43
	 **/
	IPage<AlarmLogPageVO> webList(AlarmLogPageCondition alarmLogPageCondition, Query query);

	AlarmLogPageVO detail(String id);

	List<AlarmLogOperationRecordEntity> operationRecordList(String alarmLogId);
	/**
	 * 获取用户异常日志数量
	 *
	 * @param plantId 站点id
	 * @return int
	 * <AUTHOR>
	 * @since 2024/11/21 10:06
	 **/
	public int exitsUserAlarmCount(long plantId);
	/**
	 * 获取代理商异常日志数量
	 *
	 * @param plantId 站点id
	 * @return int
	 * <AUTHOR>
	 * @since 2024/11/21 10:06
	 **/
	public int exitsAgentAlarmCount(long plantId);
}
