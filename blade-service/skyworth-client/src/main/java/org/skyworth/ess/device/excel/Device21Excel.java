/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.excel;


import lombok.Data;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 设备/逆变器表，记录2.1数据 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class Device21Excel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 站点ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点ID")
	private Long plantId;
	/**
	 * 逆变器/设备SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("逆变器/设备SN")
	private String deviceSerialNumber;
	/**
	 * modbus协议版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("modbus协议版本")
	private String modbusProtocolVersion;
	/**
	 * 设备/逆变器型号
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备/逆变器型号")
	private String deviceModel;
	/**
	 * 主机软件版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("主机软件版本")
	private String masterSoftwareVersion;
	/**
	 * 主机软件建立日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("主机软件建立日期")
	private String masterSoftwareBuildDate;
	/**
	 * 从机固件版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("从机固件版本")
	private String slaveFirmwareVersion;
	/**
	 * 从机固件建立日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("从机固件建立日期")
	private String slaveFirmwareBuildDate;
	/**
	 * mppt路数
	 */
	@ColumnWidth(20)
	@ExcelProperty("mppt路数")
	private String mpptNumber;
	/**
	 * 额定电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("额定电压")
	private BigDecimal ratedVoltage;
	/**
	 * 额定频率
	 */
	@ColumnWidth(20)
	@ExcelProperty("额定频率")
	private BigDecimal ratedFrequency;
	/**
	 * 额定功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("额定功率")
	private String ratedPower;
	/**
	 * 电网相数
	 */
	@ColumnWidth(20)
	@ExcelProperty("电网相数")
	private String gridPhaseNumber;
	/**
	 * EMS固件版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("EMS固件版本")
	private String emsFirmwareVersion;
	/**
	 * EMS固件建立日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("EMS固件建立日期")
	private String emsFirmwareBuildDate;
	/**
	 * DCDC固件版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("DCDC固件版本")
	private String dcdcFirmwareVersion;
	/**
	 * DCDC固件建立日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("DCDC固件建立日期")
	private String dcdcFirmwareBuildDate;
	/**
	 * wifi棒SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("wifi棒SN")
	private String wifiStickSerialNumber;
	/**
	 * 设备状态
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备状态")
	private String deviceStatus;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}
