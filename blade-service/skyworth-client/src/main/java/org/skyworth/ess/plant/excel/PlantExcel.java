/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 站点信息表 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class PlantExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 站点名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点名称")
	private String plantName;
	/**
	 * 国家
	 */
	@ColumnWidth(20)
	@ExcelProperty("国家")
	private String countryCode;
	/**
	 * 省
	 */
	@ColumnWidth(20)
	@ExcelProperty("省")
	private String provinceCode;
	/**
	 * 城市
	 */
	@ColumnWidth(20)
	@ExcelProperty("城市")
	private String cityCode;
	/**
	 * 区县
	 */
	@ColumnWidth(20)
	@ExcelProperty("区县")
	private String countyCode;
	/**
	 * 详细地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("详细地址")
	private String detailAddress;
	/**
	 * 时区
	 */
	@ColumnWidth(20)
	@ExcelProperty("时区")
	private String timeZone;
	/**
	 * 光伏数量
	 */
	@ColumnWidth(20)
	@ExcelProperty("光伏数量")
	private Integer photovoltaicNumber;
	/**
	 * 逆变器数量
	 */
	@ColumnWidth(20)
	@ExcelProperty("逆变器数量")
	private Integer deviceNumber;
	/**
	 * 储能数量
	 */
	@ColumnWidth(20)
	@ExcelProperty("储能数量")
	private Integer batteryNumber;
	/**
	 * 充电桩数量
	 */
	@ColumnWidth(20)
	@ExcelProperty("充电桩数量")
	private Integer chargingStation;
	/**
	 * 安装容量
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装容量")
	private String installCapacity;
	/**
	 * 安装日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装日期")
	private Date installDate;
	/**
	 * 安装团队
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装团队")
	private String installTeam;
	/**
	 * 运维团队
	 */
	@ColumnWidth(20)
	@ExcelProperty("运维团队")
	private String operationTeam;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 站点状态，由站点下不同设备（逆变器、电池等）汇总，如果其中一个异常站点则异常
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点状态，由站点下不同设备（逆变器、电池等）汇总，如果其中一个异常站点则异常")
	private String plantStatus;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}
