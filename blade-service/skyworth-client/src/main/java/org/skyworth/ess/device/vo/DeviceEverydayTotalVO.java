/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.device.entity.DeviceEverydayTotalEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备/逆变器每日统计 视图实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceEverydayTotalVO extends DeviceEverydayTotalEntity {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "开始时间")
	private Date startDateTime;

	@ApiModelProperty(value = "结束时间")
	private Date endDateTime;
	// Today Energy 0x1027减去Today export Energy 0x1334
	private BigDecimal fromPv = new BigDecimal("0");
	// Today import Energy 0x1332
	private BigDecimal fromGrid = new BigDecimal("0");
	// 1027
	private BigDecimal sumTodayEnergy;
	// 1334
	private BigDecimal sumTodayExportEnergy;
	// 1336
	private BigDecimal sumTodayLoadEnergy;
	// 1360
	private BigDecimal sumDailyEnergyToEps;
	// 200B
	private BigDecimal sumBatteryDailyChargeEnergy;
	// 1332
	private BigDecimal sumTodayImportEnergy;
	// begin 并机
	// 13D8
	private BigDecimal sumPvlDailyGeneratingEnergySum;
	// 13AC
	private BigDecimal sumDailyEnergyOfLoadSum;
	// 13C0
	private BigDecimal sumDailySupportEnergySumToBackup;
	// 13E0
	private BigDecimal sumBatteryDailyChargeEnergyParallel;
	// end 并机

}
