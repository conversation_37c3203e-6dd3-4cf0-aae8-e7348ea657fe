package org.skyworth.ess.operationlog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity;
import org.skyworth.ess.operationlog.vo.AdvancedSettingsOperationLogVO;

import java.util.List;
import java.util.Map;

/**
 * 高级设置操作日志 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
public interface AdvancedSettingsOperationLogMapper extends BaseMapper<AdvancedSettingsOperationLogEntity> {

	List<AdvancedSettingsOperationLogVO> getLogList(IPage<AdvancedSettingsOperationLogVO> page,@Param("map") Map<String, Object> map);
}
