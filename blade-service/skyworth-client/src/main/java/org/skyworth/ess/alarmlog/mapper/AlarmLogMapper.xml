<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.alarmlog.mapper.AlarmLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="alarmLogResultMap" type="org.skyworth.ess.alarmlog.entity.AlarmLogEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="exception_type" property="exceptionType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="department_id" property="departmentId"/>
        <result column="user_id" property="userId"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="exception_message" property="exceptionMessage"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="address_code" property="addressCode"/>
        <result column="time_zone" property="timeZone"/>
        <result column="alarm_number" property="alarmNumber"/>
    </resultMap>
    <select id="list" resultType="org.skyworth.ess.alarmlog.vo.AlarmLogPageVO">
        select
        al.id ,
        al.plant_id as plantId,
        p.plant_name as plantName,
        al.exception_type as exceptionType,
        al.serial_number as serialNumber,
        al.device_date_time as deviceDateTime,
        al.time_zone as timeZone,
        if(al.status = 1,
        al.update_time,
        null) as recoveryTime,
        al.exception_message as exceptionMessage,
        al.status,
        al.address_code as addressCode,
        al.alarm_number as alarmNumber,
        al.update_user_account as alarmHandler
        from
        alarm_log al
        left join plant p on
        al.plant_id = p.id
        where
        al.is_deleted = 0
        and p.is_deleted = 0
        and DATEDIFF(al.device_date_time, CURDATE()) &lt; 365
        <if test="params.departmentId!=null and params.departmentId!= ''">
            and (
            find_in_set(al.department_id, #{params.departmentId}) != 0
            or al.user_id = #{params.userId}
            )
        </if>
        <if test="params.userId!=null and params.userId!=''">
            and al.plant_id not in (select plant_id from plant_agent_unauthorized_user where
            unauthorized_user_id=#{params.userId} and is_deleted = 0 )
        </if>
        <if test="params.startDateTime!=null and params.startDateTime!=''">
            <![CDATA[ and al.device_date_time >= STR_TO_DATE(#{params.startDateTime},'%Y-%m-%d %H:%i:%s')]]>
        </if>
        <if test="params.endDateTime!=null and params.endDateTime!=''">
            <![CDATA[ and al.device_date_time <= STR_TO_DATE(#{params.endDateTime},'%Y-%m-%d %H:%i:%s')]]>
        </if>
        <if test="params.exceptionType!=null and params.exceptionType!=''">
            and al.exception_type = #{params.exceptionType}
        </if>
        <if test="params.serialNumber!=null and params.serialNumber!=''">
            and al.serial_number like concat(#{params.serialNumber}, '%')
        </if>
        <if test="params.status!=null">
            and al.status = #{params.status}
        </if>
        <if test="params.plantId!=null and params.plantId!=''">
            and al.plant_id = #{params.plantId}
        </if>
        <if test="params.alarmHandler!=null and params.alarmHandler!=''">
            <choose>
                <when test="params.alarmHandler=='user'">
                    and al.user_id is not null
                </when>
                <otherwise>
                    and al.user_id is null and al.department_id is not null
                </otherwise>
            </choose>
        </if>
        order by al.status asc, al.device_date_time desc, al.id desc
    </select>

    <select id="webList" resultType="org.skyworth.ess.alarmlog.vo.AlarmLogPageVO">
        select
        al.id ,
        al.plant_id as plantId,
        p.plant_name as plantName,
        al.exception_type as exceptionType,
        al.serial_number as serialNumber,
        CONVERT_TZ(al.device_date_time,'+00:00',#{params.timeZone}) as deviceDateTime,
        if(al.status = 1,
        CONVERT_TZ(al.update_time,'+00:00',#{params.timeZone}),
        null) as recoveryTime,
        al.exception_message as exceptionMessage,
        al.status,
        al.address_code as addressCode,
        al.alarm_number as alarmNumber,
        al.update_user_account as alarmHandler
        from
        alarm_log al
        left join plant p on
        al.plant_id = p.id
        where
        al.is_deleted = 0
        and p.is_deleted = 0
        and DATEDIFF(al.device_date_time, CURDATE()) &lt; 365
        <if test="params.departmentId!=null and params.departmentId!= ''">
            and (
            find_in_set(al.department_id, #{params.departmentId}) != 0
            or al.user_id = #{params.userId}
            )
        </if>
        <if test="params.userId!=null and params.userId!=''">
            and al.plant_id not in (select plant_id from plant_agent_unauthorized_user where
            unauthorized_user_id=#{params.userId} and is_deleted = 0 )
        </if>
        <if test="params.startDateTime!=null and params.startDateTime!=''">
            and al.device_date_time <![CDATA[ >= ]]>
            STR_TO_DATE(CONVERT_TZ(#{params.startDateTime},#{params.timeZone},'+00:00'),'%Y-%m-%d
            %H:%i:%s')
        </if>
        <if test="params.endDateTime!=null and params.endDateTime!=''">
            and al.device_date_time <![CDATA[ <= ]]>
            STR_TO_DATE(CONVERT_TZ(#{params.endDateTime},#{params.timeZone},'+00:00'),'%Y-%m-%d
            %H:%i:%s')
        </if>
        <if test="params.exceptionType!=null and params.exceptionType!=''">
            and al.exception_type = #{params.exceptionType}
        </if>
        <if test="params.serialNumber!=null and params.serialNumber!=''">
            and al.serial_number like concat(#{params.serialNumber}, '%')
        </if>
        <if test="params.status!=null">
            and al.status = #{params.status}
        </if>
        <if test="params.plantId!=null and params.plantId!=''">
            and al.plant_id = #{params.plantId}
        </if>
        <if test="params.alarmHandler!=null and params.alarmHandler!=''">
            <choose>
                <when test="params.alarmHandler=='user'">
                    and al.user_id is not null
                </when>
                <otherwise>
                    and al.user_id is null and al.department_id is not null
                </otherwise>
            </choose>
        </if>
        order by al.status asc, al.device_date_time desc, al.id desc
    </select>

    <select id="detail" resultType="org.skyworth.ess.alarmlog.vo.AlarmLogPageVO">
        select
        al.id ,
        al.plant_id as plantId,
        p.plant_name as plantName,
        al.exception_type as exceptionType,
        al.serial_number as serialNumber,
        al.device_date_time as deviceDateTime,
        al.time_zone as timeZone,
        if(al.status = 1, al.update_time, null) as recoveryTime,
        al.exception_message as exceptionMessage,
        al.status,
        al.address_code as addressCode,
        al.alarm_number as alarmNumber,
        al.update_user_account as alarmHandler
        from
        alarm_log al
        left join plant p on
        al.plant_id = p.id
        where
        al.is_deleted = 0
        and p.is_deleted = 0
        and al.id = #{id}
    </select>


    <select id="queryUpdateRecord" resultType="org.skyworth.ess.alarmlog.vo.AlarmLogPageVO">
        select al.id,
            al.serial_number as serialNumber,
            al.exception_type as exceptionType,
            al.plant_id as plantId,
            al.alarm_number as alarmNumber,
            al.address_code as addressCode
        from alarm_log al
        left join plant p on
        al.plant_id = p.id
        where
        p.is_deleted = 0
        and al.is_deleted = 0
        and al.plant_id = #{plantId}
        and al.exception_type = #{type}
        and al.status != 1
        and al.serial_number = #{serialNumber}
        <if test="list!=null">
            and (al.address_code,al.alarm_number) not in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                (#{item.addressCode,jdbcType=VARCHAR},#{item.alarmNumber,jdbcType=INTEGER})
            </foreach>
        </if>
    </select>

    <select id="queryDeleteRecord" resultType="org.skyworth.ess.alarmlog.vo.AlarmLogPageVO">
        select al.id,
        al.serial_number as serialNumber,
        al.exception_type as exceptionType,
        al.plant_id as plantId,
        al.alarm_number as alarmNumber,
        al.address_code as addressCode
        from alarm_log al
        left join plant p on
        al.plant_id = p.id
        where
        p.is_deleted = 0
        and al.is_deleted = 0
        and al.plant_id = #{plantId}
        and al.serial_number = #{serialNumber}
        and al.status != 1
        <if test="list != null">
            and (al.address_code, al.alarm_number) in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                (#{item.addressCode}, #{item.alarmNumber})
            </foreach>
        </if>
    </select>

    <select id="getDistinctKeys" resultType="java.lang.String">
        select
        concat(al.plant_id, ':', al.serial_number, ':', al.exception_type, ':', al.address_code, ':', al.alarm_number) as key1
        from
        alarm_log al
        left join plant p on
        al.plant_id = p.id
        where
        p.is_deleted = 0
        and al.is_deleted = 0
        and al.plant_id = #{plantId}
        and al.serial_number = #{serialNumber}
        and al.status != 1
        and al.exception_type in('battery', 'device')
    </select>

    <select id="exitsUserAlarmCount" resultType="java.lang.Integer">
        select count(1) from alarm_log al
        left join plant p on
        al.plant_id = p.id
        where
        p.is_deleted = 0
        and al.is_deleted = 0
        and al.plant_id = #{plantId}
        and al.status != 1
        and al.exception_type in('battery', 'device')
        and al.user_id is not null
    </select>

    <select id="exitsAgentAlarmCount" resultType="java.lang.Integer">
        select count(1) from alarm_log al
        left join plant p on
        al.plant_id = p.id
        where
        p.is_deleted = 0
        and al.is_deleted = 0
        and al.plant_id = #{plantId}
        and al.status != 1
        and al.exception_type in('battery', 'device')
        and al.department_id is not null
    </select>
</mapper>
