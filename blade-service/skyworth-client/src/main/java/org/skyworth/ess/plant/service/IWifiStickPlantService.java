/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.app.vo.AppVO;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.excel.WifiStickPlantExcel;
import org.skyworth.ess.plant.vo.WifiStickPlantVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * wifi棒对应站点表 服务类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public interface IWifiStickPlantService extends BaseService<WifiStickPlantEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param WifiStickPlant
	 * @return
	 */
	IPage<WifiStickPlantVO> selectWifiStickPlantPage(IPage<WifiStickPlantVO> page, WifiStickPlantVO WifiStickPlant);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<WifiStickPlantExcel> exportWifiStickPlant(Wrapper<WifiStickPlantEntity> queryWrapper);

	/**
	 * 根据站点id信息查询设备厂商
	 * @param appVO
	 * @return
	 */
	List<WifiStickPlantVO> queryDeviceCompany(AppVO appVO);

	List<WifiStickPlantEntity> queryDeviceSerialNumberList(List<Long> list);
	int batchDeleteLogicByPlantId(List<Long> plantIdList,String updateUserAccount);

	List<WifiStickPlantEntity> queryByDeviceSerialNumberList(List<String> list);

	List<WifiStickPlantEntity> queryOwnerData(Long createUser);

	int updateDataByCondition(WifiStickPlantEntity updateOwner);

    List<WifiStickPlantEntity> getWifiStickInfo(List<WifiStickPlantEntity> wifiStickPlantEntities);

	void updateStartupByBackstage(WifiStickPlantEntity wifiStickPlant);

    void batchDeleteLogicByPlantIdAndSn(Long plantId, String deviceSerialNumber, String account);

	List<Long> getWifiStickInfoAndBatteryInfo(List<Long> longPlantIdList, String deviceSerialNumber);
}
