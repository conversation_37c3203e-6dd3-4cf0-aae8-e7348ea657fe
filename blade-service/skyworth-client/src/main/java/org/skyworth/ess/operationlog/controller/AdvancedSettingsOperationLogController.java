package org.skyworth.ess.operationlog.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.operationlog.service.IAdvancedSettingsOperationLogService;
import org.skyworth.ess.operationlog.vo.AdvancedSettingsOperationLogVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

/**
 * 高级设置操作日志 控制器
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/advancedSettingsOperationLog")
@Api(value = "高级设置操作日志", tags = "高级设置操作日志接口")
public class AdvancedSettingsOperationLogController extends BladeController {

	private final IAdvancedSettingsOperationLogService advancedSettingsOperationLogService;

	/**
	 * 高级设置操作日志 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入advancedSettingsOperationLog")
	public R<IPage<AdvancedSettingsOperationLogVO>> list(@ApiIgnore @RequestParam Map<String, Object> map,
														 Query query) {
		return R.data(advancedSettingsOperationLogService.getLogList(Condition.getPage(query), map));
	}

	/**
	 * web端高级设置操作日志 分页
	 */
	@GetMapping("/web/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "web端分页", notes = "传入advancedSettingsOperationLog")
	public R<IPage<AdvancedSettingsOperationLogVO>> webLogList(@ApiIgnore @RequestParam Map<String, Object> map,
															Query query) {
		return R.data(advancedSettingsOperationLogService.getWebLogList(Condition.getPage(query), map));
	}
}
