/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * wifi棒对应站点表 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class WifiStickPlantExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * wifi棒SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("wifi棒SN")
	private String wifiStickSerialNumber;
	/**
	 * wifi棒版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("wifi棒版本")
	private String wifiStickVersion;
	/**
	 * 站点ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点ID")
	private Long plantId;
	/**
	 * 逆变器/设备SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("逆变器/设备SN")
	private String deviceSerialNumber;
	/**
	 * wifi棒状态（1在线；0离线）
	 */
	@ColumnWidth(20)
	@ExcelProperty("wifi棒状态（1在线；0离线）")
	private String wifiStickStatus;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}
