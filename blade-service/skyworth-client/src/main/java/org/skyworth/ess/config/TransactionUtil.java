package org.skyworth.ess.config;


import lombok.AllArgsConstructor;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.interceptor.DefaultTransactionAttribute;

/**
 * 自定义事务
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-12-06 09:53
 **/
@Component
@AllArgsConstructor
public class TransactionUtil {

	private final DataSourceTransactionManager dataSourceTransactionManager;

	/**
	 * 开启事务
	 */
	public TransactionStatus begin() {
		//默认传播行为，不设置事务的隔离级别
		return dataSourceTransactionManager.getTransaction(new DefaultTransactionAttribute());
	}

	/**
	 * 提交事务
	 */
	public void commit(TransactionStatus transactionStatus) {
		dataSourceTransactionManager.commit(transactionStatus);
	}

	/**
	 * 回滚事务
	 */
	public void rollBack(TransactionStatus transactionStatus) {
		dataSourceTransactionManager.rollback(transactionStatus);
	}
}

