/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 光伏板站点设备/逆变器表 实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@TableName("photovoltaic_plant_device")
@ApiModel(value = "PhotovoltaicPlantDevice对象", description = "光伏板站点设备/逆变器表")
@EqualsAndHashCode(callSuper = true)
public class PhotovoltaicPlantDeviceEntity extends TenantEntity {

	/**
	 * mppt型号(mppt1/2)
	 */
	@ApiModelProperty(value = "mppt型号(mppt1/2)")
	private String mpptType;
	/**
	 * 光伏板SN
	 */
	@ApiModelProperty(value = "光伏板SN")
	private String photovoltaicSerialNumber;
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ApiModelProperty(value = "设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}
