/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 设备/逆变器23数据 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class Device23Excel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 站点id
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点id")
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * modbus协议版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("modbus协议版本")
	private String modbusProtocolVersion;
	/**
	 * 工作模式
	 */
	@ColumnWidth(20)
	@ExcelProperty("工作模式")
	private String hybridWorkMode;
	/**
	 * 一次/每天
	 */
	@ColumnWidth(20)
	@ExcelProperty("一次/每天")
	private String onceEveryday;
	/**
	 * 充电开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("充电开始时间")
	private String chargeStartTime1;
	/**
	 * 充电结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("充电结束时间")
	private String chargeEndTime1;
	/**
	 * 放电开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("放电开始时间")
	private String dischargeStartTime1;
	/**
	 * 放电结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("放电结束时间")
	private String dischargeEndTime1;
	/**
	 * 电池类型选择
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池类型选择")
	private String batteryTypeSelection;
	/**
	 * 通讯地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("通讯地址")
	private String commAddress;
	/**
	 * 电池容量
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池容量")
	private BigDecimal batteryAh;
	/**
	 * 铅酸电池放电截止电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("铅酸电池放电截止电压")
	private BigDecimal stopDischargeVoltage;
	/**
	 * 铅酸电池充电截止电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("铅酸电池充电截止电压")
	private BigDecimal stopChargeVoltage;
	/**
	 * 电网充电使能
	 */
	@ColumnWidth(20)
	@ExcelProperty("电网充电使能")
	private String gridCharge;
	/**
	 * 最大电网充电功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最大电网充电功率")
	private BigDecimal maximumGridChargerPower;
	/**
	 * 电网充电截止电量%
	 */
	@ColumnWidth(20)
	@ExcelProperty("电网充电截止电量%")
	private BigDecimal capacityOfGridChargerEnd;
	/**
	 * 最大充电功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最大充电功率")
	private BigDecimal maximumChargerPower;
	/**
	 * 充电截止SOC
	 */
	@ColumnWidth(20)
	@ExcelProperty("充电截止SOC")
	private BigDecimal capacityOfChargerEnd;
	/**
	 * 最大放电功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最大放电功率")
	private BigDecimal maximumDischargerPower;
	/**
	 * 放电截止EOD
	 */
	@ColumnWidth(20)
	@ExcelProperty("放电截止EOD")
	private BigDecimal capacityOfDischargerEnd;
	/**
	 * 离网模式使能
	 */
	@ColumnWidth(20)
	@ExcelProperty("离网模式使能")
	private String offGridMode;
	/**
	 * 额定输出电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("额定输出电压")
	private BigDecimal ratedOutputVoltage;
	/**
	 * 额定输出频率
	 */
	@ColumnWidth(20)
	@ExcelProperty("额定输出频率")
	private BigDecimal ratedOutputFrequency;
	/**
	 * 切换离网模式的电池最低SOC
	 */
	@ColumnWidth(20)
	@ExcelProperty("切换离网模式的电池最低SOC")
	private String offGridStartUpBatteryCapacity;
	/**
	 * 最大放电电流
	 */
	@ColumnWidth(20)
	@ExcelProperty("最大放电电流")
	private BigDecimal maximumDischargeCurrent;
	/**
	 * 最大充电电流
	 */
	@ColumnWidth(20)
	@ExcelProperty("最大充电电流")
	private BigDecimal maximumChargerCurrent;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String synchStatus;

}
