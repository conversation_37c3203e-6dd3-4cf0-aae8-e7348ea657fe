<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.plant.mapper.PhotovoltaicPlantDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="PhotovoltaicPlantDeviceResultMap" type="org.skyworth.ess.plant.entity.PhotovoltaicPlantDeviceEntity">
        <result column="id" property="id"/>
        <result column="mppt_type" property="mpptType"/>
        <result column="photovoltaic_serial_number" property="photovoltaicSerialNumber"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectPhotovoltaicPlantDevicePage" resultMap="PhotovoltaicPlantDeviceResultMap">
        select * from photovoltaic_plant_device where is_deleted = 0
    </select>


    <select id="exportPhotovoltaicPlantDevice" resultType="org.skyworth.ess.plant.excel.PhotovoltaicPlantDeviceExcel">
        SELECT * FROM photovoltaic_plant_device ${ew.customSqlSegment}
    </select>

</mapper>
