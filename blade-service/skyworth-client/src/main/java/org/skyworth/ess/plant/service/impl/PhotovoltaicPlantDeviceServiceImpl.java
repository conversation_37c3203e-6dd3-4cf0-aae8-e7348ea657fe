/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.service.impl;

import org.skyworth.ess.plant.entity.PhotovoltaicPlantDeviceEntity;
import org.skyworth.ess.plant.vo.PhotovoltaicPlantDeviceVO;
import org.skyworth.ess.plant.excel.PhotovoltaicPlantDeviceExcel;
import org.skyworth.ess.plant.mapper.PhotovoltaicPlantDeviceMapper;
import org.skyworth.ess.plant.service.IPhotovoltaicPlantDeviceService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 光伏板站点设备/逆变器表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Service
public class PhotovoltaicPlantDeviceServiceImpl extends BaseServiceImpl<PhotovoltaicPlantDeviceMapper, PhotovoltaicPlantDeviceEntity> implements IPhotovoltaicPlantDeviceService {

	@Override
	public IPage<PhotovoltaicPlantDeviceVO> selectPhotovoltaicPlantDevicePage(IPage<PhotovoltaicPlantDeviceVO> page, PhotovoltaicPlantDeviceVO PhotovoltaicPlantDevice) {
		return page.setRecords(baseMapper.selectPhotovoltaicPlantDevicePage(page, PhotovoltaicPlantDevice));
	}


	@Override
	public List<PhotovoltaicPlantDeviceExcel> exportPhotovoltaicPlantDevice(Wrapper<PhotovoltaicPlantDeviceEntity> queryWrapper) {
		List<PhotovoltaicPlantDeviceExcel> PhotovoltaicPlantDeviceList = baseMapper.exportPhotovoltaicPlantDevice(queryWrapper);
		//PhotovoltaicPlantDeviceList.forEach(PhotovoltaicPlantDevice -> {
		//	PhotovoltaicPlantDevice.setTypeName(DictCache.getValue(DictEnum.YES_NO, PhotovoltaicPlantDevice.getType()));
		//});
		return PhotovoltaicPlantDeviceList;
	}

}
