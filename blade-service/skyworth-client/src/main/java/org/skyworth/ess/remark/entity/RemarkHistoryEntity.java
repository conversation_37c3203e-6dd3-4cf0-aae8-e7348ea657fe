package org.skyworth.ess.remark.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.EqualsAndHashCode;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.system.entity.AttachmentInfoEntity;

/**
 * 站点评论记录表 实体类
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
@TableName("plant_remark_history")
@ApiModel(value = "RemarkHistory对象", description = "站点评论记录表")
@EqualsAndHashCode(callSuper = true)
public class RemarkHistoryEntity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 评论
	 */
	@ApiModelProperty(value = "评论")
	private String remark;
	/**
	 * 附件批次id
	 */
	@ApiModelProperty(value = "附件批次id")
	private Long businessId;
	/**
	 * 昵称
	 */
	@ApiModelProperty(value = "昵称")
	private String remarkUserName;

	/**
	 * 附件
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "附件展示")
	private BatchVO<AttachmentInfoEntity> batchVO;

	@TableField(exist = false)
	@ApiModelProperty(value = "附件保存对象")
	private List<AttachmentInfoEntity> attachmentInfoEntity;

	@TableField(exist = false)
	@ApiModelProperty(value = "时区")
	private String timeZone;
}
