/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmlog.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.service.IAlarmLogService;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageCondition;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageVO;
import org.skyworth.ess.alarmlog.vo.AlarmLogVO;
import org.skyworth.ess.alarmlog.wrapper.AlarmLogWrapper;
import org.skyworth.ess.constant.DateTimeTypeEnum;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

/**
 * 异常日志表 控制器
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/alarmLog")
@Api(value = "异常日志表", tags = "异常日志表接口")
public class AlarmLogController extends BladeController {

	private final IAlarmLogService alarmLogService;

	/**
	 * 异常日志表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入alarmLog")
	public R<AlarmLogVO> detail(AlarmLogEntity alarmLog) {
		AlarmLogEntity detail = alarmLogService.getOne(Condition.getQueryWrapper(alarmLog));
		return R.data(AlarmLogWrapper.build().entityVO(detail));
	}

	/**
	 * 异常日志表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入alarmLog")
	public R<IPage<AlarmLogPageVO>> list(AlarmLogPageCondition alarmLogPageCondition, Query query) {
		String timeType = alarmLogPageCondition.getTimeType();
		if (StringUtils.isNotBlank(timeType)) {
			DateTimeTypeEnum timeTypeEnum = DateTimeTypeEnum.getDateTimeTypeEnum(alarmLogPageCondition.getTimeType());
			switch (Objects.requireNonNull(timeTypeEnum)) {
				case YEAR:
					alarmLogPageCondition.setStartDateTime(DateUtil.getFirstDayOfYear(alarmLogPageCondition.getStartDateTime()));
					alarmLogPageCondition.setEndDateTime(DateUtil.getLastDayOfYear(alarmLogPageCondition.getEndDateTime()));
					break;
				case MONTH:
					alarmLogPageCondition.setStartDateTime(DateUtil.completeStartDayTime(alarmLogPageCondition.getStartDateTime()));
					alarmLogPageCondition.setEndDateTime(DateUtil.completeEndDayTime(alarmLogPageCondition.getEndDateTime()));
					break;
				default:
					break;
			}
		}
		IPage<AlarmLogPageVO> pages = alarmLogService.webList(alarmLogPageCondition, query);
		return R.data(pages);
	}

	/**
	 * 异常日志表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入alarmLog")
	public R save(@Valid @RequestBody AlarmLogEntity alarmLog) {
		return R.status(alarmLogService.save(alarmLog));
	}

	/**
	 * 异常日志表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入alarmLog")
	public R update(@Valid @RequestBody AlarmLogEntity alarmLog) {
		return R.status(alarmLogService.updateById(alarmLog));
	}

	/**
	 * 异常日志表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入alarmLog")
	public R submit(@Valid @RequestBody AlarmLogEntity alarmLog) {
		return R.status(alarmLogService.saveOrUpdate(alarmLog));
	}

	/**
	 * 异常日志表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(alarmLogService.deleteLogic(Func.toLongList(ids)));
	}
}
