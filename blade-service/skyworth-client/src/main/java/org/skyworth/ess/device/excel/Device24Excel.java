/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Device24 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class Device24Excel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String year;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String monthDay;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String hourMinute;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String second;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String powerDeratingPercentByModbus;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String modbusAddress;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String rs485BaudRate;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String wifiStaSsid;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String wifiStaPassword;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal digitalMeterModbusAddress;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String digitalMeterType;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String powerFlowDirection;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String powerLimitFunction;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String powerLimitCtRatio;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String meterLocation;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal maximumFeedInGridPower;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal firstConnectStartTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal reconnectTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyHighLossLevel1Limit;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyLowLossLevel1Limit;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageHighLossLevel1Limit;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageLowLossLevel1Limit;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyHighLossLevel1TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyLowLossLevel1TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageHighLossLevel1TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageLowLossLevel1TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyHighLossLevel2Limit;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyLowLossLevel2Limit;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageHighLossLevel2Limit;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageLowLossLevel2Limit;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyHighLossLevel2TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyLowLossLevel2TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageHighLossLevel2TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageLowLossLevel2TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyHighLevel1Back;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyLowLevel1Back;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	@TableField(value = "ten_min_average_sustained_voltage")
	private BigDecimal tenMinAverageSustainedVoltage;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal reconnectSoftOutputPowerPercent;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal overFrequencyPowerReductionDroop;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal insulationResistanceActiveLimit;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridOverVoltageDeratingPoint;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyHighLevel1TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridFrequencyLowLevel1TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridOverFrequencyDeratingStartPoint;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridOverFrequencyDeratingEndPoint;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageHighLevel1TripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageLowLevel1tripTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageHighLevel1Back;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal gridVoltageLowLevel1Back;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal firstConnectSoftStartOutputPowerPercent;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal overVoltageDeratingSettlingTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal connectionAndReconnectionPowerRampRate;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal outputReactivePowerMode;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal powerFactorSetting;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal reactiveControlResponseTime;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal curveNode1Percent;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal curveNode2Percent;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal curveNode3Percent;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal curveNode4Percent;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal curveNode1ValueSetting;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal curveNode2ValueSetting;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private BigDecimal curveNode3ValueSetting;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String regulationCode;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String inverterControl;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String factoryReset;
	/**
	 *
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String clearHistoricalInfo;
	/**
	 * 站点id
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点id")
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * modbus协议版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("modbus协议版本")
	private String modbusProtocolVersion;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备时间，设备上报时时间")
	private Date deviceDateTime;

}
