/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 设备/逆变器当前状态 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class DeviceCurrentStatusExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 站点ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点ID")
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 总能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("总能量")
	private BigDecimal totalEnergy;
	/**
	 * 总发电时间（小时）
	 */
	@ColumnWidth(20)
	@ExcelProperty("总发电时间（小时）")
	private BigDecimal totalGenerationTime;
	/**
	 * 今日能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("今日能量")
	private BigDecimal todayEnergy;
	/**
	 * 累计正极能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("累计正极能量")
	private BigDecimal accumulatedEnergyOfPositive;
	/**
	 * 累计负极能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("累计负极能量")
	private BigDecimal accumulatedEnergyOfNegative;
	/**
	 * 累计负载能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("累计负载能量")
	private BigDecimal accumulatedEnergyOfLoad;
	/**
	 * 今日输入能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("今日输入能量")
	private BigDecimal todayImportEnergy;
	/**
	 * 今日输出能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("今日输出能量")
	private BigDecimal todayExportEnergy;
	/**
	 * 今日负载能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("今日负载能量")
	private BigDecimal todayLoadEnergy;
	/**
	 * 日均EPS能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("日均EPS能量")
	private BigDecimal dailyEnergyToEps;
	/**
	 * 累计EPS能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("累计EPS能量")
	private BigDecimal accumulatedEnergyToEps;
	/**
	 * 电池SOC
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池SOC")
	private BigDecimal batterySoc;
	/**
	 * 电池日均充电能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池日均充电能量")
	private BigDecimal batteryDailyChargeEnergy;
	/**
	 * 电池日均放电能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池日均放电能量")
	private BigDecimal batteryDailyDischargeEnergy;
	/**
	 * 电池累计充电能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池累计充电能量")
	private BigDecimal batteryAccumulatedChargeEnergy;
	/**
	 * 电池累计放电能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池累计放电能量")
	private BigDecimal batteryAccumulatedDischargeEnergy;
	/**
	 * 逆变器模式
	 */
	@ColumnWidth(20)
	@ExcelProperty("逆变器模式")
	private String inverterMode;
	/**
	 * MPPT1电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT1电压")
	private BigDecimal pv1Voltage;
	/**
	 * MPPT1电流
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT1电流")
	private BigDecimal pv1Current;
	/**
	 * MPPT1功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT1功率")
	private BigDecimal mppt1Power;
	/**
	 * MPPT2电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT2电压")
	private BigDecimal pv2Voltage;
	/**
	 * MPPT2电流
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT2电流")
	private BigDecimal pv2Current;
	/**
	 * MPPT2功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT2功率")
	private BigDecimal mppt2Power;
	/**
	 * MPPT3电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT3电压")
	private BigDecimal pv3Voltage;
	/**
	 * MPPT3电流
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT3电流")
	private BigDecimal pv3Current;
	/**
	 * MPPT3功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT3功率")
	private BigDecimal mppt3Power;
	/**
	 * MPPT4电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT4电压")
	private BigDecimal pv4Voltage;
	/**
	 * MPPT4电流
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT4电流")
	private BigDecimal pv4Current;
	/**
	 * MPPT4功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("MPPT4功率")
	private BigDecimal mppt4Power;
	/**
	 * 电池电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池电压")
	private BigDecimal batteryVoltage;
	/**
	 * 电池电流
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池电流")
	private BigDecimal batteryCurrent;
	/**
	 * 电池功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池功率")
	private BigDecimal batteryPower;
	/**
	 * 电压phase a
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压phase a")
	private BigDecimal phaseAVoltage;
	/**
	 * 电流phase a
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流phase a")
	private BigDecimal phaseACurrent;
	/**
	 * 功率phase a
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率phase a")
	private BigDecimal phaseAPower;
	/**
	 * 电压phase b
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压phase b")
	private BigDecimal phaseBVoltage;
	/**
	 * 电流phase b
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流phase b")
	private BigDecimal phaseBCurrent;
	/**
	 * 功率phase b
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率phase b")
	private BigDecimal phaseBPower;
	/**
	 * 电压phase c
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压phase c")
	private BigDecimal phaseCVoltage;
	/**
	 * 电流phase c
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流phase c")
	private BigDecimal phaseCCurrent;
	/**
	 * 功率phase c
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率phase c")
	private BigDecimal phaseCPower;
	/**
	 * 电压 r of grid
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压 r of grid")
	private BigDecimal l1NPhaseVoltageOfGrid;
	/**
	 * 电流 r of grid
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流 r of grid")
	private BigDecimal l1CurrentOfGrid;
	/**
	 * 功率 r of grid
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率 r of grid")
	private BigDecimal phaseRWattOfGrid;
	/**
	 * 电压 s of grid
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压 s of grid")
	private BigDecimal l2NPhaseVoltageOfGrid;
	/**
	 * 电流 s of grid
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流 s of grid")
	private BigDecimal l2CurrentOfGrid;
	/**
	 * 功率 s of grid
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率 s of grid")
	private BigDecimal phaseSWattOfGrid;
	/**
	 * 电压 t of grid
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压 t of grid")
	private BigDecimal l3NPhaseVoltageOfGrid;
	/**
	 * 电流 t of grid
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流 t of grid")
	private BigDecimal l3CurrentOfGrid;
	/**
	 * 功率 t of grid
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率 t of grid")
	private BigDecimal phaseTWattOfGrid;
	/**
	 * 电压 r of load
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压 r of load")
	private BigDecimal l1NPhaseVoltageOfLoad;
	/**
	 * 电流 r of load
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流 r of load")
	private BigDecimal l1CurrentOfLoad;
	/**
	 * 功率 r of load
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率 r of load")
	private BigDecimal phaseRWattOfLoad;
	/**
	 * 电压 s of load
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压 s of load")
	private BigDecimal l2NPhaseVoltageOfLoad;
	/**
	 * 电流 s of load
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流 s of load")
	private BigDecimal l2CurrentOfLoad;
	/**
	 * 功率 s of load
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率 s of load")
	private BigDecimal phaseSWattOfLoad;
	/**
	 * 电压 t of load
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压 t of load")
	private BigDecimal l3NPhaseVoltageOfLoad;
	/**
	 * 电流 t of load
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流 t of load")
	private BigDecimal l3CurrentOfLoad;
	/**
	 * 功率  t of load
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率  t of load")
	private BigDecimal phaseTWattOfLoad;
	/**
	 * 电压 r of eps
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压 r of eps")
	private BigDecimal phaseRVoltageOfEps;
	/**
	 * 电流  r of eps
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流  r of eps")
	private BigDecimal phaseRCurrentOfEps;
	/**
	 * 功率  r of eps
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率  r of eps")
	private BigDecimal phaseRWattOfEps;
	/**
	 * 电压  s of eps
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压  s of eps")
	private BigDecimal phaseSVoltageOfEps;
	/**
	 * 电流  s of eps
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流  s of eps")
	private BigDecimal phaseSCurrentOfEps;
	/**
	 * 功率  s of eps
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率  s of eps")
	private BigDecimal phaseSWattOfEps;
	/**
	 * 电压 t of eps
	 */
	@ColumnWidth(20)
	@ExcelProperty("电压 t of eps")
	private BigDecimal phaseTVoltageOfEps;
	/**
	 * 电流 t of eps
	 */
	@ColumnWidth(20)
	@ExcelProperty("电流 t of eps")
	private BigDecimal phaseTCurrentOfEps;
	/**
	 * 功率  t of eps
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率  t of eps")
	private BigDecimal phaseTWattOfEps;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}
