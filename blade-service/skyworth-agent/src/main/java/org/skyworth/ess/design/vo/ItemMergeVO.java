package org.skyworth.ess.design.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class ItemMergeVO implements Serializable {
	private static final long serialVersionUID = 1L;
	// 物料基础包
	private List<ProductVO> itemBasePackageList;
	// 物料自定义
	private List<ProductVO> itemCustomizeList;
	// 物料基础包总价
	private BigDecimal itemBasePackageTotalPrice;
	// 物料自定义总价
	private BigDecimal itemCustomizeTotalPrice;
	// 物料总价
	private BigDecimal itemTotalPrice;
	// 物料包名称
	private String itemBasePackageName;
	// 物料包编码
	private String itemBasePackage;
	// 最终价格
	private BigDecimal itemFinalTotalPrice;
	// 报价单第一项价格
	private BigDecimal itemFirstTotalPrice;
	// 报价单特殊价格列表
	private List<ItemSeparatePriceVO> itemSeparatePriceVOList;
	// 库存管理员是否审批
	private Date warehouseApproveCreateTime;

}
