/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.skyworth.ess.design.designinfo.entity.DesignInfoEntity;
import org.skyworth.ess.design.designinfo.mapper.DesignInfoMapper;
import org.skyworth.ess.design.designinfo.service.IDesignInfoService;
import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;
import org.skyworth.ess.design.deviceItem.service.IDeviceItemService;
import org.skyworth.ess.design.dto.DesignSubmitDTO;
import org.skyworth.ess.design.service.IDesignService;
import org.skyworth.ess.design.vo.DesignVO;
import org.skyworth.ess.design.vo.ItemMergeVO;
import org.skyworth.ess.revieworder.orderworkflow.service.IAttachmentInfoService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static org.skyworth.ess.constant.OrderStatusConstants.EXAMINE_APPROVE_PASS;


/**
 * 设计服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
public class DesignServiceImpl extends BaseServiceImpl<DesignInfoMapper,DesignInfoEntity> implements IDesignService {
	@Autowired
	private IDesignInfoService designInfoService;

	@Autowired
	private IDeviceItemService deviceItemService;

	@Autowired
	private IReviewOrderService reviewOrderService;

	@Autowired
	private IAttachmentInfoService attachmentInfoService;

	@Override
	public DesignVO designDetail(Long orderId) {
		DesignVO designVO = new DesignVO();
		LambdaQueryWrapper<DesignInfoEntity> houseStructureWrapper = new LambdaQueryWrapper<>();
		houseStructureWrapper.eq(DesignInfoEntity::getOrderId, orderId);
		DesignInfoEntity designInfoEntity = designInfoService.getOne(houseStructureWrapper);
		designVO.setAttachmentDesc(attachmentInfoService.getAttachmentInfo(designInfoEntity));
		ItemMergeVO itemMergeVO = deviceItemService.queryProductList(orderId);
		designVO.setItemMergeVO(itemMergeVO);
		designVO.setDesignInfoEntity(designInfoEntity);
		return designVO;
	}

	@Override
	public boolean save(DesignSubmitDTO designSubmitDTO) {
		// 验证逻辑
		checkSaveDeviceItem(designSubmitDTO);

		// 保存设计图片信息
		if (Objects.nonNull(designSubmitDTO.getDesignInfo())) {
			designSubmitDTO.getDesignInfo().setOrderId(designSubmitDTO.getOrderId());
			designInfoService.saveOrUpdate(designSubmitDTO.getDesignInfo());
		}

		// 保存产品列表信息
		LambdaQueryWrapper<DeviceItemEntity> deviceItemWrapper = new LambdaQueryWrapper<>();
		deviceItemWrapper.eq(DeviceItemEntity::getOrderId, designSubmitDTO.getOrderId());
		deviceItemService.remove(deviceItemWrapper);
		designSubmitDTO.getProductList().forEach(e -> e.setOrderId(designSubmitDTO.getOrderId()));
		deviceItemService.saveBatch(designSubmitDTO.getProductList());

		attachmentInfoService.saveAttachmentInfo(designSubmitDTO);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(DesignSubmitDTO designSubmitDTO) {
		// 验证逻辑
		Map<String, Object> variables = designSubmitDTO.getOrderFlowDTO().getVariables();
		String examineApproveType = String.valueOf(variables.get("examineApproveType"));
		if (EXAMINE_APPROVE_PASS.equals(examineApproveType)) {
			checkDeviceItem(designSubmitDTO);

			// 保存设计图片信息
			designSubmitDTO.getDesignInfo().setOrderId(designSubmitDTO.getOrderId());
			designInfoService.saveOrUpdate(designSubmitDTO.getDesignInfo());

			// 保存产品列表信息
			LambdaQueryWrapper<DeviceItemEntity> deviceItemWrapper = new LambdaQueryWrapper<>();
			deviceItemWrapper.eq(DeviceItemEntity::getOrderId, designSubmitDTO.getOrderId());
			deviceItemService.remove(deviceItemWrapper);
			designSubmitDTO.getProductList().forEach(e -> e.setOrderId(designSubmitDTO.getOrderId()));
			deviceItemService.saveBatch(designSubmitDTO.getProductList());
		}

		attachmentInfoService.saveAttachmentInfo(designSubmitDTO);

		// 调用审批流接口进行流程流转
		R<?> examineApproveResult = reviewOrderService.examineApprove(designSubmitDTO.getOrderFlowDTO());
		return examineApproveResult.isSuccess();
	}


	private void checkSaveDeviceItem(DesignSubmitDTO designSubmitDTO) {
		if (designSubmitDTO.getDesignInfo().getId() == null) {
			LambdaQueryWrapper<DesignInfoEntity> houseStructureWrapper = new LambdaQueryWrapper<>();
			houseStructureWrapper.eq(DesignInfoEntity::getOrderId, designSubmitDTO.getOrderId());
			DesignInfoEntity designInfoEntity = designInfoService.getOne(houseStructureWrapper);
			if (designInfoEntity != null) {
				throw new BusinessException("design.save.designInfoAlreadySaved");
			}
		}

		if (CollectionUtils.isNotEmpty(designSubmitDTO.getProductList())) {
			Map<String, DeviceItemEntity> deviceItemMap = new HashMap<>();
			for (DeviceItemEntity deviceItemEntity : designSubmitDTO.getProductList()) {
				if (Objects.isNull(deviceItemMap.get(deviceItemEntity.getItemCode()))) {
					deviceItemMap.put(deviceItemEntity.getItemCode(), deviceItemEntity);
				} else {
					throw new BusinessException("design.productList.invalid", deviceItemEntity.getItemCode());
				}
			}
		}
	}

	private void checkDeviceItem(DesignSubmitDTO designSubmitDTO) {

		if (Objects.isNull(designSubmitDTO.getDesignInfo())) {
			throw new BusinessException("design.attachmentInfo.notNull");
		}

		if (Objects.isNull(designSubmitDTO.getDesignInfo().getId())) {
			LambdaQueryWrapper<DesignInfoEntity> houseStructureWrapper = new LambdaQueryWrapper<>();
			houseStructureWrapper.eq(DesignInfoEntity::getOrderId, designSubmitDTO.getOrderId());
			DesignInfoEntity designInfoEntity = designInfoService.getOne(houseStructureWrapper);
			if (designInfoEntity != null) {
				throw new BusinessException("design.save.designInfoAlreadySaved");
			}
		}

		Map<String, DeviceItemEntity> deviceItemMap = new HashMap<>();
		for (DeviceItemEntity deviceItemEntity : designSubmitDTO.getProductList()) {
			if (Objects.isNull(deviceItemMap.get(deviceItemEntity.getItemCode()))) {
				deviceItemMap.put(deviceItemEntity.getItemCode(), deviceItemEntity);
			} else {
				throw new BusinessException("design.productList.invalid", deviceItemEntity.getItemCode());
			}
		}

	}


}
