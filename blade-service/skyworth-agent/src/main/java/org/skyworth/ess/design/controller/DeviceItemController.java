/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;

import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;
import org.skyworth.ess.design.deviceItem.excel.DeviceItemExcel;
import org.skyworth.ess.design.deviceItem.service.IDeviceItemService;
import org.skyworth.ess.design.deviceItem.vo.DeviceItemVO;
import org.skyworth.ess.design.deviceItem.wrapper.DeviceItemWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 设备备料清单 控制器
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-deviceItem/deviceItem")
@Api(value = "设备备料清单", tags = "设备备料清单接口")
public class DeviceItemController extends BladeController {

	private final IDeviceItemService deviceItemService;

	/**
	 * 设备备料清单 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceItem")
	public R<DeviceItemVO> detail(DeviceItemEntity deviceItem) {
		DeviceItemEntity detail = deviceItemService.getOne(Condition.getQueryWrapper(deviceItem));
		return R.data(DeviceItemWrapper.build().entityVO(detail));
	}
	/**
	 * 设备备料清单 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入deviceItem")
	public R<IPage<DeviceItemVO>> list(@ApiIgnore @RequestParam Map<String, Object> deviceItem, Query query) {
		IPage<DeviceItemEntity> pages = deviceItemService.page(Condition.getPage(query), Condition.getQueryWrapper(deviceItem, DeviceItemEntity.class));
		return R.data(DeviceItemWrapper.build().pageVO(pages));
	}

	/**
	 * 设备备料清单 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入deviceItem")
	public R<IPage<DeviceItemVO>> page(DeviceItemVO deviceItem, Query query) {
		IPage<DeviceItemVO> pages = deviceItemService.selectDeviceItemPage(Condition.getPage(query), deviceItem);
		return R.data(pages);
	}

	/**
	 * 设备备料清单 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入deviceItem")
	public R save(@Valid @RequestBody DeviceItemEntity deviceItem) {
		return R.status(deviceItemService.save(deviceItem));
	}

	/**
	 * 设备备料清单 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入deviceItem")
	public R update(@Valid @RequestBody DeviceItemEntity deviceItem) {
		return R.status(deviceItemService.updateById(deviceItem));
	}

	/**
	 * 设备备料清单 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入deviceItem")
	public R submit(@Valid @RequestBody DeviceItemEntity deviceItem) {
		return R.status(deviceItemService.saveOrUpdate(deviceItem));
	}

	/**
	 * 设备备料清单 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(deviceItemService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-deviceItem")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入deviceItem")
	public void exportDeviceItem(@ApiIgnore @RequestParam Map<String, Object> deviceItem, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<DeviceItemEntity> queryWrapper = Condition.getQueryWrapper(deviceItem, DeviceItemEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(DeviceItem::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(DeviceItemEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<DeviceItemExcel> list = deviceItemService.exportDeviceItem(queryWrapper);
		ExcelUtil.export(response, "设备备料清单数据" + DateUtil.time(), "设备备料清单数据表", list, DeviceItemExcel.class);
	}

}
