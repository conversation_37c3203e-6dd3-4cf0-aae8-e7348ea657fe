<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.design.designinfo.mapper.DesignInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="designInfoResultMap" type="org.skyworth.ess.design.designinfo.entity.DesignInfoEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="design_drawing_doc_biz_key" property="designDrawingDocBizKey"/>
        <result column="wiring_diagram_doc_biz_key" property="wiringDiagramDocBizKey"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectDesignInfoPage" resultMap="designInfoResultMap">
        select * from design_info where is_deleted = 0
    </select>


    <select id="exportDesignInfo" resultType="org.skyworth.ess.design.designinfo.excel.DesignInfoExcel">
        SELECT * FROM design_info ${ew.customSqlSegment}
    </select>

</mapper>
