<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.design.deviceItem.mapper.DeviceItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceItemResultMap" type="org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_type" property="itemType"/>
        <result column="standards" property="standards"/>
        <result column="model" property="model"/>
        <result column="unit" property="unit"/>
        <result column="quantity" property="quantity"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectDeviceItemPage" resultMap="deviceItemResultMap">
        select * from design_device_item where is_deleted = 0
    </select>


    <select id="exportDeviceItem" resultType="org.skyworth.ess.design.deviceItem.excel.DeviceItemExcel">
        SELECT * FROM design_device_item ${ew.customSqlSegment}
    </select>


    <select id="queryProductListByOrderId" resultType="org.skyworth.ess.design.vo.ProductVO">
        SELECT design.id,design.item_code as itemCode, design.quantity as quantity,sku.sku_name as skuName,sku.sku_company skuCompany,sku.standards,
        design.confirm_flag as confirmFlag,sku.sku_device_type as skuDeviceType,sku.unit,
        case when design.item_price is null then ifnull(sku.price,0)*ifnull(design.quantity,0)
        else ifnull(design.item_price,0)*ifnull(design.quantity,0) end as orderItemTotalPrice,
        design.item_price as orderItemPrice,sku.price as baseSkuPrice
        FROM design_device_item design left join  sku_base_info sku on design.item_code = sku.sku_code and sku.is_deleted = 0
        where design.order_id=#{orderId} and design.is_deleted = 0 and (design.item_base_package is null or design.item_base_package='')
    </select>

    <select id="querySkuOutQuantity" resultMap="deviceItemVOMap">
        select i.item_code ,sum(ifnull(i.quantity,0)) as out_quantity from design_device_item i inner join business_order_work_flow w on i.order_id =w.order_id
        where i.is_deleted =0 and w.is_deleted =0 and w.warehouse_approve_create_time is not null
        <if test="params.beginDate != null">
            <![CDATA[ and DATE_FORMAT(w.warehouse_approve_create_time,'%Y-%m-%d') >= DATE_FORMAT(#{params.beginDate},'%Y-%m-%d')  ]]>
        </if>
        <if test="params.endDate != null">
            <![CDATA[ and DATE_FORMAT(w.warehouse_approve_create_time,'%Y-%m-%d') <= DATE_FORMAT(#{params.endDate},'%Y-%m-%d')  ]]>
        </if>
        <if test="params.beforeBeginDate != null">
            <![CDATA[ and DATE_FORMAT(w.warehouse_approve_create_time,'%Y-%m-%d') < DATE_FORMAT(#{params.beforeBeginDate},'%Y-%m-%d')  ]]>
        </if>
        <if test="list != null and list.size() > 0">
            and i.item_code in
            <foreach collection="list" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
        group by i.item_code
    </select>

    <select id="queryBasePackageProductListByOrderId" resultMap="productVOMap">
        select
        design.id,design.item_code ,design.quantity,design.item_base_package,design.item_price ,
        sku.sku_name ,sku.sku_company , sku.standards, design.confirm_flag ,sku.sku_device_type,sku.unit,sku.price,
        ifnull(design.item_price, 0)* ifnull(design.quantity, 0) as orderItemTotalPrice
        from design_device_item design left join sku_base_info sku on
        design.item_code = sku.sku_code and sku.is_deleted = 0
        where design.is_deleted = 0
        and design.item_base_package = #{itemBasePackage} and design.order_id = #{orderId}
    </select>

    <update id="updateBaseSkuPrice" >
        update design_info set item_base_package_price = #{basePackagePrice},item_final_total_price = #{itemFinalTotalPrice},update_time = now() where order_id = #{orderId}
    </update>

    <update id="batchUpdateSkuInfoPrice">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update design_device_item set item_price = #{item.itemPrice},update_time = now() where order_id = #{item.orderId}
            and id = #{item.id}
        </foreach>
    </update>

    <resultMap id="productVOMap" type="org.skyworth.ess.design.vo.ProductVO">
        <result column="id" property="id"/>
        <result column="item_base_package" property="itemBasePackage"/>
        <result column="item_code" property="itemCode"/>
        <result column="confirm_flag" property="confirmFlag"/>
        <result column="quantity" property="quantity"/>
        <result column="sku_name" property="skuName"/>
        <result column="sku_company" property="skuCompany"/>
        <result column="standards" property="standards"/>
        <result column="sku_device_type" property="skuDeviceType"/>
        <result column="unit" property="unit"/>
        <result column="price" property="baseSkuPrice"/>
        <result column="item_price" property="orderItemPrice"/>
        <result column="orderItemTotalPrice" property="orderItemTotalPrice"/>
    </resultMap>
    <resultMap id="deviceItemVOMap" type="org.skyworth.ess.design.deviceItem.vo.DeviceItemVO">
        <result column="item_code" property="itemCode"/>
        <result column="out_quantity" property="outQuantity"/>
    </resultMap>

</mapper>
