package org.skyworth.ess.survey.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.skyworth.ess.revieworder.orderworkflow.vo.AttachmentDescVO;

/**
 * 签名信息 实体类
 *
 * <AUTHOR>
 * @since 2023-12-20
 */
@Data
public class SurveySignVO {
	/**
	 * 踏勘人员签名图片key
	 */
	@ApiModelProperty(value = "踏勘人员签名图片key")
	private Long technicianSignImgBizKey;

	/**
	 * 电气人员签名图片key
	 */
	@ApiModelProperty(value = "电气人员签名图片key")
	private Long electricianOwnerImgBizKey;


	/**
	 * 业主签名图片key
	 */
	@ApiModelProperty(value = "业主签名图片key")
	private Long landlordSignImgBizKey;


	private AttachmentDescVO attachmentDesc;
}
