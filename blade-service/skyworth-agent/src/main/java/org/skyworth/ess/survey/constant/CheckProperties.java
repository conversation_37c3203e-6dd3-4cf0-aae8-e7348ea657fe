package org.skyworth.ess.survey.constant;

import java.util.Arrays;
import java.util.List;

public class CheckProperties {
	public static List<String> checkHouseStructure = Arrays.asList("typeOfInstall", "typeOfInstallRemark", "accessEasy", "accessSpecify", "accessSpecify",
		"buildingPermit", "buildingPermitRemark", "electricalPermit", "electricalPermitRemark", "zoningPermit", "zoningPermitRemark", "structure", "frameType",
		"houseType", "roofType", "roofMaterial", "roofSize", "roofFaceDirection", "roofFaceSideAngel", "roofFaceSideSlope", "roofGutter",
		"roofGutterRemark", "roofOcclusion", "mountingStructure", "panelType", "panelOrientation", "relevantDoc", "specialRequest");

	public static List<String> checkHouseElectrical = Arrays.asList("infrastructure", "infrastructureConnectionType", "backupGenerator", "backupGeneratorQty", "backupGeneratorTotalCapacity",
		"backupPowerSource", "backupPowerSourceQty", "backupPowerSourceTotalCapacity", "electricalCondition", "electricalConditionRemark", "routingConfirmation", "routingConfirmationRemark",
		"routingDraft", "inspectWiring", "upgradeRequired", "powerMonthly", "powerAnnual");

}
