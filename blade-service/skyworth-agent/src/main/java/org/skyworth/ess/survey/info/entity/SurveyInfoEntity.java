/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.info.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 踏勘信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@TableName("survey_info")
@ApiModel(value = "SurveyInfo对象", description = "踏勘信息")
@EqualsAndHashCode(callSuper = true)
public class SurveyInfoEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	@NotNull(message = "{agent.createOrder.orderId.notNull}")
	private Long orderId;
	/**
	 * 踏勘人员签名图片key
	 */
	@ApiModelProperty(value = "踏勘人员签名图片key")
	private Long technicianSignImgBizKey;

	/**
	 * 踏勘人员拍照图片key
	 */
	@ApiModelProperty(value = "踏勘人员拍照图片key")
	private Long technicianPhotoImgBizKey;


	/**
	 * 踏勘最终完成时间（计算出来）
	 */
	@ApiModelProperty(value = "踏勘最终完成时间（计算出来）")
	private Date surveyFinalFinishDate;
	/**
	 * 电气人员签名图片key
	 */
	@ApiModelProperty(value = "电气人员签名图片key")
	private Long electricianOwnerImgBizKey;

	/**
	 * 电气人员拍照图片key
	 */
	@ApiModelProperty(value = "电气人员拍照图片key")
	private Long electricianOwnerPhotoImgBizKey;



	/**
	 * 业主签名图片key
	 */
	@ApiModelProperty(value = "业主签名图片key")
	private Long landlordSignImgBizKey;
	/**
	 * 业主签名时间
	 */
	@ApiModelProperty(value = "业主签名时间")
	private Date landlordSignDate;
	/**
	 * 踏勘提交时间
	 */
	@ApiModelProperty(value = "踏勘提交时间")
	private Date surveySubmitDate;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;


	/**
	 * 踏勘人员id
	 */
	@TableField(exist = false)
	@NotNull(message = "{agent.surveyAssignment.technicianId.notNull}")
	private Long technicianId;

	/**
	 * 电气人员id
	 */
	@TableField(exist = false)
	@NotNull(message = "{agent.surveyAssignment.electricianId.notNull}")
	private Long electricianId;


	/**
	 * 踏勘预计时间
	 */
	@TableField(exist = false)
	private Date surveyDate;

	/**
	 * 踏勘时间确认
	 */
	@TableField(exist = false)
	private Date surveyDateConfirm;


	/**
	 * 踏勘日期更改原因
	 */
	@TableField(exist = false)
	private String surveyDateConfirmRemark;



	/**
	 * 踏勘人员名称
	 */
	@TableField(exist = false)
	@NotBlank(message = "{agent.surveyAssignment.technicianName.notNull}")
	private String technicianName;


	/**
	 * 电气人员名称
	 */
	@TableField(exist = false)
	@NotBlank(message = "{agent.surveyAssignment.electricianName.notNull}")
	private String electricianName;
}
