/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.deviceItem.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;
import org.skyworth.ess.design.deviceItem.excel.DeviceItemExcel;
import org.skyworth.ess.design.deviceItem.mapper.DeviceItemMapper;
import org.skyworth.ess.design.deviceItem.service.IDeviceItemService;
import org.skyworth.ess.design.deviceItem.vo.DeviceItemVO;
import org.skyworth.ess.design.dto.StockAuditDTO;
import org.skyworth.ess.design.vo.ItemMergeVO;
import org.skyworth.ess.design.vo.ItemSeparatePriceVO;
import org.skyworth.ess.design.vo.ProductVO;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.sku.entity.SkuBaseInfoEntity;
import org.skyworth.ess.sku.service.ISkuBaseInfoService;
import org.skyworth.ess.sku.service.ISkuHistoryService;
import org.skyworth.ess.sku.vo.SkuHistoryVO;
import org.skyworth.ess.vo.OrderWorkFlowVO;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static org.skyworth.ess.constant.OrderStatusConstants.EXAMINE_APPROVE_PASS;

/**
 * 设备备料清单 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
@Slf4j
public class DeviceItemServiceImpl extends BaseServiceImpl<DeviceItemMapper, DeviceItemEntity> implements IDeviceItemService {
	@Autowired
	private IDictBizClient dictBizClient;

	@Autowired
	private IReviewOrderService reviewOrderService;

	@Autowired
	private IOrderWorkFlowService orderWorkFlowService;

	@Lazy
	@Autowired
	private ISkuHistoryService skuHistoryService;

	@Autowired
	private ISkuBaseInfoService skuBaseInfoService;

	@Override
	public IPage<DeviceItemVO> selectDeviceItemPage(IPage<DeviceItemVO> page, DeviceItemVO deviceItem) {
		return page.setRecords(baseMapper.selectDeviceItemPage(page, deviceItem));
	}

	@Override
	public int updateDesignBaseSkuPrice(Long orderId, BigDecimal basePackagePrice, BigDecimal itemFinalTotalPrice) {
		return baseMapper.updateBaseSkuPrice(orderId, basePackagePrice, itemFinalTotalPrice);
	}

	@Override
	public List<DeviceItemExcel> exportDeviceItem(Wrapper<DeviceItemEntity> queryWrapper) {
		List<DeviceItemExcel> deviceItemList = baseMapper.exportDeviceItem(queryWrapper);
		//deviceItemList.forEach(deviceItem -> {
		//	deviceItem.setTypeName(DictCache.getValue(DictEnum.YES_NO, DeviceItem.getType()));
		//});
		return deviceItemList;
	}

	@Override
	public int batchUpdateSkuInfoPrice(List<DeviceItemEntity> list) {
		return baseMapper.batchUpdateSkuInfoPrice(list);
	}

	private List<ProductVO> queryProductListByOrderId(Long orderId, ItemMergeVO itemMergeVO, Map<String, String> skuTypeMap) {
		List<ProductVO> list = baseMapper.queryProductListByOrderId(orderId);
		if (CollectionUtil.isNotEmpty(list)) {
			BigDecimal customizeTotalPrice = BigDecimal.ZERO;
			for (ProductVO e : list) {
				e.setSkuDeviceTypeName(skuTypeMap.get(e.getSkuDeviceType()));
				customizeTotalPrice = customizeTotalPrice.add(e.getOrderItemTotalPrice());
				// 如果库存管理员未审批前， 2个价格设置成一样
				if (e.getOrderItemPrice() == null) {
					e.setOrderItemPrice(e.getBaseSkuPrice());
				}
			}
			itemMergeVO.setItemCustomizeTotalPrice(customizeTotalPrice == null ? new BigDecimal("0.00") : customizeTotalPrice.setScale(2, RoundingMode.HALF_UP));
		}
		return list;
	}

	private List<ProductVO> queryProductListByOrderId4BasePackage(Long orderId, ItemMergeVO itemMergeVO, Map<String, String> skuTypeMap) {
		List<ProductVO> list = new ArrayList<>();

		// 获取订单是否通过了 库存管理员
		OrderWorkFlowVO orderWorkFlowVO = orderWorkFlowService.queryWorkFlowWithDesign(orderId);
		if (orderWorkFlowVO == null || StringUtils.isEmpty(orderWorkFlowVO.getItemBasePackage())) {
			return list;
		}
		R<List<DictBiz>> agentItemBasePackage = dictBizClient.queryChildByDictKey(DictBizCodeEnum.AGENT_ITEM_BASE_PACKAGE.getDictCode(),
			orderWorkFlowVO.getItemBasePackage(), CommonUtil.getCurrentLanguage());
		List<DictBiz> data = agentItemBasePackage.getData();
		String basePackageName = data.stream().filter(p -> orderWorkFlowVO.getItemBasePackage().equals(p.getDictKey())).map(DictBiz::getDictValue).findAny().orElse("");
		itemMergeVO.setItemBasePackage(orderWorkFlowVO.getItemBasePackage());
		itemMergeVO.setItemBasePackageName(basePackageName);
		// 如果库存管理员没有审批通过，则物料基础包的 数量取 快码 agent_item_base_package 中下一层级的字段 属性1 ，
		// 价格取 sku_base_info 中的 price
		if (orderWorkFlowVO.getWarehouseApproveCreateTime() == null) {
			log.info("queryProductListByOrderId4BasePackage WarehouseApproveCreateTime is null ");
			List<String> skuCodeList = data.stream().map(DictBiz::getDictKey).collect(Collectors.toList());
			// 取 业务字典上的 每个物料的数量
			Map<String, String> skuCodeMapQuantity = data.stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getAttribute1));
			// 库存管理员 未审批时，取业务字典的 总价
			String itemBasePackageTotalPrice = skuCodeMapQuantity.get(orderWorkFlowVO.getItemBasePackage()) == null ? "0" : skuCodeMapQuantity.get(orderWorkFlowVO.getItemBasePackage());
			itemMergeVO.setItemBasePackageTotalPrice(new BigDecimal(itemBasePackageTotalPrice).setScale(2, RoundingMode.HALF_UP));
			// 根据物料编码查询物料对应的价格和基本信息
			List<SkuBaseInfoEntity> skuBaseInfoEntities = skuBaseInfoService.querySkuBaseInfoBySkuCodeList(skuCodeList);
			for (SkuBaseInfoEntity skuBaseInfo : skuBaseInfoEntities) {
				String skuCode = skuBaseInfo.getSkuCode();
				BigDecimal price = skuBaseInfo.getPrice() == null ? new BigDecimal("0.00") : skuBaseInfo.getPrice().setScale(2, RoundingMode.HALF_UP);
				String quantity = skuCodeMapQuantity.get(skuCode);
				BigDecimal totalPrice = price.multiply(new BigDecimal(quantity));
				ProductVO productVO = new ProductVO();
				productVO.setOrderItemTotalPrice(totalPrice == null ? new BigDecimal("0.00") : totalPrice.setScale(2, RoundingMode.HALF_UP));
				productVO.setItemCode(skuBaseInfo.getSkuCode());
				productVO.setSkuName(skuBaseInfo.getSkuName());
				productVO.setSkuDeviceType(skuBaseInfo.getSkuDeviceType());
				productVO.setSkuDeviceTypeName(skuTypeMap.get(skuBaseInfo.getSkuDeviceType()));
				productVO.setStandards(skuBaseInfo.getStandards());
				productVO.setSkuCompany(skuBaseInfo.getSkuCompany());
				productVO.setUnit(skuBaseInfo.getUnit());
				productVO.setQuantity(Long.valueOf(quantity));
				productVO.setBaseSkuPrice(price);
				productVO.setOrderItemPrice(price);
				productVO.setItemBasePackage(orderWorkFlowVO.getItemBasePackage());
				list.add(productVO);
			}
			log.info("queryProductListByOrderId4BasePackage WarehouseApproveCreateTime is not null end ");
		} else {
			itemMergeVO.setWarehouseApproveCreateTime(orderWorkFlowVO.getWarehouseApproveCreateTime());
			log.info("queryProductListByOrderId4BasePackage WarehouseApproveCreateTime is not null ");
			list = baseMapper.queryBasePackageProductListByOrderId(orderId, orderWorkFlowVO.getItemBasePackage());
			for (ProductVO e : list) {
				e.setSkuDeviceTypeName(skuTypeMap.get(e.getSkuDeviceType()));
			}
			BigDecimal basePackagePrice = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
			// 库存管理员 审批通过取 踏勘主表上落地的总价
			if (orderWorkFlowVO.getItemBasePackagePrice() != null) {
				basePackagePrice = orderWorkFlowVO.getItemBasePackagePrice().setScale(2, RoundingMode.HALF_UP);
			}
			itemMergeVO.setItemBasePackageTotalPrice(basePackagePrice);
			itemMergeVO.setItemFinalTotalPrice(orderWorkFlowVO.getItemFinalTotalPrice());
		}
		list.sort(Comparator.comparing(ProductVO::getItemCode));
		log.info("queryProductListByOrderId4BasePackage end ");
		return list;
	}

	@Override
	public ItemMergeVO queryProductList(Long orderId) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R<List<DictBiz>> listByLang = dictBizClient.getListByLang(DictBizCodeEnum.AGENT_SKU_DEVICE_TYPE.getDictCode(), currentLanguage);
		Map<String, String> skuTypeMap = listByLang.getData().stream().collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey, (p, p1) -> p));
		ItemMergeVO itemMergeVO = new ItemMergeVO();
		List<ProductVO> customize = this.queryProductListByOrderId(orderId, itemMergeVO, skuTypeMap);
		List<ProductVO> basePackage = this.queryProductListByOrderId4BasePackage(orderId, itemMergeVO, skuTypeMap);
		itemMergeVO.setItemCustomizeList(customize);
		itemMergeVO.setItemBasePackageList(basePackage);
		BigDecimal basePrice = itemMergeVO.getItemBasePackageTotalPrice() == null ? BigDecimal.ZERO : itemMergeVO.getItemBasePackageTotalPrice();
		BigDecimal customizePrice = itemMergeVO.getItemCustomizeTotalPrice() == null ? BigDecimal.ZERO : itemMergeVO.getItemCustomizeTotalPrice();
		itemMergeVO.setItemTotalPrice(basePrice.add(customizePrice));
		List<DeviceItemEntity> customizeItemList = new ArrayList<>();
		List<SkuBaseInfoEntity> skuBaseInfoEntityList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(customize)) {
			List<Long> itemIdList = customize.stream().map(ProductVO::getId).collect(Collectors.toList());
			customizeItemList = this.listByIds(itemIdList);
			List<String> customizeItemCoke = customizeItemList.stream().map(DeviceItemEntity::getItemCode).collect(Collectors.toList());
			skuBaseInfoEntityList = skuBaseInfoService.querySkuBaseInfoBySkuCodeList(customizeItemCoke);
		}
		String basePackagePrice = itemMergeVO.getItemBasePackageTotalPrice() == null ? "0" : String.valueOf(itemMergeVO.getItemBasePackageTotalPrice());
		// 兼容 报价单第1、2项计算逻辑，否则库存管理员审批完（ItemFinalTotalPrice不为空时），不会计算报价单 第1、2项
		BigDecimal itemFinalTotalPrice = this.calItemFinalTotalPrice(customizeItemList, basePackagePrice, skuBaseInfoEntityList, itemMergeVO);
		// 库存管理员是否审批
		if (itemMergeVO.getWarehouseApproveCreateTime() == null) {
			itemMergeVO.setItemFinalTotalPrice(itemFinalTotalPrice == null ? new BigDecimal("0.00") : itemFinalTotalPrice.setScale(2, RoundingMode.HALF_UP));
		} else {
			itemMergeVO.setItemFinalTotalPrice(itemMergeVO.getItemFinalTotalPrice() == null ? new BigDecimal("0.00") : itemMergeVO.getItemFinalTotalPrice().setScale(2, RoundingMode.HALF_UP));
		}

		return itemMergeVO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean audit(StockAuditDTO stockAuditDTO) {
		List<DeviceItemEntity> productList = stockAuditDTO.getProductList();

		// 库存审核通过更新审批通过时间
		Map<String, Object> variables = stockAuditDTO.getOrderFlowDTO().getVariables();
		String examineApproveType = String.valueOf(variables.get("examineApproveType"));
		if (EXAMINE_APPROVE_PASS.equals(examineApproveType)) {
			String itemBasePackage = stockAuditDTO.getItemBasePackage();
			List<DictBiz> basePackageList = this.getItemBasePackageMap(itemBasePackage);
			// 取 业务字典上的 每个物料编码和数量，排除物料包类型本身,本身是物料包总价格
			Map<String, String> basePackageMap = basePackageList.stream().filter(p -> !p.getDictKey().equals(itemBasePackage)).collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getAttribute1));
			// 获取物料包价格
			String basePackagePrice = basePackageList.stream().filter(p -> p.getDictKey().equals(itemBasePackage)).map(DictBiz::getAttribute1).findFirst().orElse("0");
			// 验证结余库存
			List<DeviceItemEntity> customizeItemList = this.checkStock(productList, basePackageMap);
			// 更新自定义物料价格
			List<SkuBaseInfoEntity> skuBaseInfoEntityList = this.updateCustomizeItemPrice(customizeItemList);
			// 计算最终价格
			ItemMergeVO itemMergeVO = new ItemMergeVO();
			BigDecimal itemFinalTotalPrice = this.calItemFinalTotalPrice(customizeItemList, basePackagePrice, skuBaseInfoEntityList, itemMergeVO);
			// 更新设计主表物料包价格、最终价格
			if (StringUtils.isNotEmpty(itemBasePackage)) {
				log.info("stock audit save basePackage data");
				List<DeviceItemEntity> basePackageInsertList = new ArrayList<>();
				// 写入物料包 item 和 quantity，物料包不写入单价
				this.saveBasePackageItem(stockAuditDTO, basePackageMap, itemBasePackage, basePackageInsertList);
				log.info("stock audit save basePackage item data");
				// 有基础物料包
				baseMapper.updateBaseSkuPrice(Long.parseLong(stockAuditDTO.getOrderFlowDTO().getBusinessId()), new BigDecimal(basePackagePrice), itemFinalTotalPrice);
			} else {
				baseMapper.updateBaseSkuPrice(Long.parseLong(stockAuditDTO.getOrderFlowDTO().getBusinessId()), null, itemFinalTotalPrice);
			}
			UpdateWrapper<OrderWorkFlowEntity> orderWorkFlowWrapper = new UpdateWrapper<>();
			orderWorkFlowWrapper.eq("order_id", stockAuditDTO.getOrderFlowDTO().getBusinessId());

			OrderWorkFlowEntity orderWorkFlowEntity = new OrderWorkFlowEntity();
			orderWorkFlowEntity.setWarehouseApproveCreateTime(new Date());
			orderWorkFlowService.update(orderWorkFlowEntity, orderWorkFlowWrapper);
		}

		// 保存或者更新设备备料清单
		if (CollectionUtil.isNotEmpty(productList)) {
			saveOrUpdateBatch(productList);
		}

		return reviewOrderService.examineApprove(stockAuditDTO.getOrderFlowDTO()).isSuccess();
	}

	private BigDecimal calItemFinalTotalPrice(List<DeviceItemEntity> customizeItemList, String basePackagePrice, List<SkuBaseInfoEntity> skuBaseInfoEntityList, ItemMergeVO itemMergeVO) {
		BigDecimal itemFinalTotalPrice = new BigDecimal("0");

		if (CollectionUtil.isNotEmpty(customizeItemList)) {
			log.info("calItemFinalTotalPrice customizeItemList is not null basePackagePrice : {}", basePackagePrice);
			Map<String, BigDecimal> itemPriceMap = skuBaseInfoEntityList.stream().filter(p -> p.getPrice() != null).collect(Collectors.toMap(SkuBaseInfoEntity::getSkuCode, SkuBaseInfoEntity::getPrice));
			//计算最终价格
			R<List<DictBiz>> agentItemPriceRule = dictBizClient.getListAllLang(DictBizCodeEnum.AGENT_ITEM_FINAL_PRICE_RULE.getDictCode());
			String currentLanguage = CommonUtil.getCurrentLanguage();
			List<DictBiz> dictBizList = agentItemPriceRule.getData();
			Set<String> excludeItemSet = new HashSet<>();
			Map<String, String> separateItemMap = new HashMap<>();
			if (CollectionUtil.isNotEmpty(dictBizList)) {
				Long dictId = dictBizList.stream().filter(p -> DictBizCodeEnum.AGENT_EXCLUDE_ITEM.getDictCode().equals(p.getDictKey())).map(DictBiz::getId).findAny().orElse(-100L);
				Long separateDictId = dictBizList.stream().filter(p -> DictBizCodeEnum.AGENT_SEPARATE_ITEM_PRICE.getDictCode().equals(p.getDictKey())).map(DictBiz::getId).findAny().orElse(-100L);
				excludeItemSet = dictBizList.stream().filter(p -> dictId.equals(p.getParentId())).map(DictBiz::getDictKey).collect(Collectors.toSet());
				separateItemMap = dictBizList.stream().filter(p -> separateDictId.equals(p.getParentId())).collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getAttribute1));
			}
			log.info("calItemFinalTotalPrice excludeItemSet : {}", excludeItemSet);
			log.info("calItemFinalTotalPrice separateItemMap : {}", separateItemMap);
			//	最终价格 = 产品包价格 + 自定义物料*数量*单价 - (SKY0001、SKY0045、SKY0046、SKY0047、SKY0048、SKY0049)*数量*单价
			//	 + SKY0001 * 数量 * 3500
			BigDecimal itemCustomizeTotalPrice = new BigDecimal("0.00");
			BigDecimal itemFirstTotalPrice = new BigDecimal("0.00");
			BigDecimal itemSecondTotalPrice = new BigDecimal("0.00");
			List<ItemSeparatePriceVO> itemSeparatePriceVOList = new ArrayList<>();
			for (DeviceItemEntity customizeItem : customizeItemList) {
				String itemCode = customizeItem.getItemCode();
				String quantity = customizeItem.getQuantity();
				String separatePrice = separateItemMap.get(itemCode);
				// 计算需要特殊价格计算的 物料编码 ： 配置价格 * 自定义行上数量
				if (StringUtils.isNotEmpty(separatePrice)) {
					itemCustomizeTotalPrice = itemCustomizeTotalPrice.add(new BigDecimal(quantity).multiply(new BigDecimal(separatePrice)));
					ItemSeparatePriceVO itemSeparatePriceVO = new ItemSeparatePriceVO();
					// 报价单第2项价格为单独计算价格
					itemSecondTotalPrice = itemSecondTotalPrice.add(new BigDecimal(quantity).multiply(new BigDecimal(separatePrice)));
					itemSeparatePriceVO.setItemCode(itemCode);
					itemSeparatePriceVO.setItemSeparatePrice(new BigDecimal(separatePrice).setScale(2, RoundingMode.HALF_UP));
					itemSeparatePriceVO.setItemOrderQuantity(Integer.parseInt(quantity));
					itemSeparatePriceVO.setItemSeparateTotalPrice(itemSecondTotalPrice.setScale(2, RoundingMode.HALF_UP));
					itemSeparatePriceVOList.add(itemSeparatePriceVO);
				}
				// 如果在排除的物料包中 或者 在单独计算的 物料包中，则不需要 再计算
				if (excludeItemSet.contains(itemCode) || StringUtils.isNotEmpty(separatePrice)) {
					log.info("calItemFinalTotalPrice not repeat cal itemCode : {}", itemCode);
					continue;
				}
				// 如果库存管理员审批完成，则取 物料行上的价格 进行计算，如果没有则 取物料管理中的进行计算
				if (itemMergeVO.getWarehouseApproveCreateTime() == null) {
					BigDecimal baseItemPrice = itemPriceMap.get(itemCode) == null ? BigDecimal.ZERO : itemPriceMap.get(itemCode);
					// 计算自定义物料总价
					itemCustomizeTotalPrice = itemCustomizeTotalPrice.add(baseItemPrice.multiply(new BigDecimal(quantity)));
					// 计算报价单第一项价格
					itemFirstTotalPrice = itemFirstTotalPrice.add(baseItemPrice.multiply(new BigDecimal(quantity)));
				} else {
					BigDecimal itemPrice = customizeItem.getItemPrice() == null ? BigDecimal.ZERO : customizeItem.getItemPrice();
					// 计算自定义物料总价
					itemCustomizeTotalPrice = itemCustomizeTotalPrice.add(itemPrice.multiply(new BigDecimal(quantity)));
					// 计算报价单第一项价格
					itemFirstTotalPrice = itemFirstTotalPrice.add(itemPrice.multiply(new BigDecimal(quantity)));
				}
			}
			log.info("calItemFinalTotalPrice itemCustomizeTotalPrice : {} ", itemCustomizeTotalPrice);
			itemFinalTotalPrice = itemCustomizeTotalPrice.add(new BigDecimal(basePackagePrice));
			itemMergeVO.setItemFirstTotalPrice(itemFirstTotalPrice.add(new BigDecimal(basePackagePrice)));
			itemMergeVO.setItemSeparatePriceVOList(itemSeparatePriceVOList);

		} else {
			log.info("calItemFinalTotalPrice no customize basePackagePrice: {} ", basePackagePrice);
			// 如果自定义物料包为空，则最终价格等于物料包价格
			itemFinalTotalPrice = new BigDecimal(basePackagePrice);
			itemMergeVO.setItemFirstTotalPrice(itemFinalTotalPrice);
			itemMergeVO.setItemSeparatePriceVOList(new ArrayList<>());
		}
		log.info("calItemFinalTotalPrice itemFinalTotalPrice : {} ", itemFinalTotalPrice);
		return itemFinalTotalPrice;
	}

	private void saveBasePackageItem(StockAuditDTO stockAuditDTO, Map<String, String> basePackageMap, String itemBasePackage, List<DeviceItemEntity> basePackageInsertList) {
		for (Map.Entry<String, String> entry : basePackageMap.entrySet()) {
			String itemCode = entry.getKey();
			String quantity = entry.getValue();
			DeviceItemEntity insertEntity = new DeviceItemEntity();
			insertEntity.setOrderId(Long.parseLong(stockAuditDTO.getOrderFlowDTO().getBusinessId()));
			insertEntity.setItemBasePackage(itemBasePackage);
			insertEntity.setItemCode(itemCode);
			insertEntity.setQuantity(quantity);
			insertEntity.setStockConfirmFlg(1);//库存审批通过，默认库存有
			basePackageInsertList.add(insertEntity);
		}
		this.saveBatch(basePackageInsertList);
	}

	// 返回自定义物料的基础价格
	private List<SkuBaseInfoEntity> updateCustomizeItemPrice(List<DeviceItemEntity> customizeItemLis) {
		log.info("stock audit updateCustomizeItemPrice ");
		if (CollectionUtil.isEmpty(customizeItemLis)) {
			return new ArrayList<>();
		}
		List<String> customizeItemCoke = customizeItemLis.stream().map(DeviceItemEntity::getItemCode).collect(Collectors.toList());
		List<SkuBaseInfoEntity> skuBaseInfoEntities = skuBaseInfoService.querySkuBaseInfoBySkuCodeList(customizeItemCoke);
		Map<String, BigDecimal> skuBasePriceList = skuBaseInfoEntities.stream().collect(Collectors.toMap(SkuBaseInfoEntity::getSkuCode, SkuBaseInfoEntity::getPrice));
		List<DeviceItemEntity> updatePriceList = new ArrayList<>();
		for (DeviceItemEntity deviceItem : customizeItemLis) {
			BigDecimal bigDecimal = skuBasePriceList.get(deviceItem.getItemCode());
			DeviceItemEntity update = new DeviceItemEntity();
			update.setId(deviceItem.getId());
			update.setOrderId(deviceItem.getOrderId());
			update.setItemPrice(bigDecimal);
			updatePriceList.add(update);
		}
		baseMapper.batchUpdateSkuInfoPrice(updatePriceList);
		log.info("stock audit updateCustomizeItemPrice end");
		return skuBaseInfoEntities;
	}

	private List<DictBiz> getItemBasePackageMap(String itemBasePackage) {
		List<DictBiz> basePackageList = new ArrayList<>();
		if (StringUtils.isNotEmpty(itemBasePackage)) {
			log.info("stock audit getItemBasePackageMap");
			R<List<DictBiz>> agentItemBasePackage = dictBizClient.queryChildByDictKey(DictBizCodeEnum.AGENT_ITEM_BASE_PACKAGE.getDictCode(),
				itemBasePackage, CommonUtil.getCurrentLanguage());
			basePackageList = agentItemBasePackage.getData();
		}
		return basePackageList;
	}

	@Override
	public List<DeviceItemVO> querySkuOutQuantity(DeviceItemVO query, List<String> skuCodeList) {
		return baseMapper.querySkuOutQuantity(query, skuCodeList);
	}

	private List<DeviceItemEntity> checkStock(List<DeviceItemEntity> productList, Map<String, String> basePackageMap) {
		List<Long> idList = new ArrayList<>();

		productList.forEach(e -> {
			idList.add(e.getId());
		});
		List<DeviceItemEntity> deviceItemList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(idList)) {
			deviceItemList = listByIds(idList);
		}

		Set<String> skuList = new HashSet<>();
		deviceItemList.forEach(e -> {
			skuList.add(e.getItemCode());
		});

		skuList.addAll(basePackageMap.keySet());
		log.info("stock audit checkStock basePackageMap : {}", basePackageMap);
		Map<String, String> quantityMap = deviceItemList.stream().collect(Collectors.toMap(DeviceItemEntity::getItemCode, DeviceItemEntity::getQuantity));
		Map<String, Long> skuQuantityTotalMap = new HashMap<>();
		for (String skuCode : skuList) {
			String customize = quantityMap.get(skuCode) == null ? "0" : quantityMap.get(skuCode);
			String basePackage = basePackageMap.get(skuCode) == null ? "0" : basePackageMap.get(skuCode);
			skuQuantityTotalMap.put(skuCode, Long.parseLong(customize) + Long.parseLong(basePackage));
		}
		log.info("stock audit checkStock skuQuantityTotalMap : {}", skuQuantityTotalMap);
		List<SkuHistoryVO> skuHistoryList = skuHistoryService.getSurplusQuantityBySku(skuList);
		skuHistoryList.forEach(e -> {
			if (skuQuantityTotalMap.get(e.getSkuCode()) > e.getSurplusQuantity()) {
				throw new BusinessException("stock.surplusQuantity.invalid", e.getSkuCode());
			}
		});
		return deviceItemList;
	}
}
