/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.service;

import org.skyworth.ess.survey.dto.*;
import org.skyworth.ess.survey.vo.EmailVO;
import org.skyworth.ess.survey.vo.SurveyInfoVO;
import org.skyworth.ess.survey.vo.SurveyPhotoVO;
import org.skyworth.ess.survey.vo.SurveySignVO;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.SkyWorthFileEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * 踏勘服务类接口
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
public interface ISurveyService {
	boolean saveOrUpdateSurvey(SurveyInfoDTO surveyInfoDTO);

	SurveyInfoVO surveyDetail(SurveyDetailDTO surveyDetailDTO);

	boolean surveySign(SurveySignDTO surveySignDTO);

	SurveySignVO surveySignDetail(String orderId);

	boolean ownerPhoto(OwnerPhotoDTO ownerPhotoDTO);

	SurveyPhotoVO ownerPhotoDetail(String orderId);

	boolean submit(SurveySubmitDTO surveySubmitDTO,String userType);

	boolean financeSubmit(OrderFlowDTO orderFlowDTO,SkyWorthFileEntity skyWorthFileEntity);

	boolean sendMail(MultipartFile[] multipartFile,SendEmailDTO sendEmailDTO);

	EmailVO emailDetail(String businessId);

	boolean paymentConfirmSendMail(MultipartFile[] multipartFile, PaymentConfirmSendEmailDTO sendEmailDTO);

	EmailVO paymentEmailDetail(String businessId);

}
