package org.skyworth.ess.design.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class ProductVO implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;

	// sku编码
	private String itemCode;

	// 数量
	private Long quantity;

	// 物料名称
	private String skuName;

	//物料厂商
	private String skuCompany;

	// 型号
	private String model;

	// 设备类型
	private String skuDeviceType;
	private String skuDeviceTypeName;

	private String unit;
	// 物料基础包
	private String itemBasePackage;
	// 物料订单价格，仓库管理员审核后确定
	private BigDecimal orderItemPrice;
	// 物料基础价格
	private BigDecimal baseSkuPrice;
	// 订单物料行总价： 物料订单每行总价
	private BigDecimal orderItemTotalPrice;
	// 规格
	private String standards;

	// 收货确认（1确认收货）
	private int confirmFlag;

}
