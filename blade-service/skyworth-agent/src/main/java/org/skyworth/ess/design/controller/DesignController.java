/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.design.dto.DesignSubmitDTO;
import org.skyworth.ess.design.service.IDesignService;
import org.skyworth.ess.design.vo.DesignVO;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springframework.web.bind.annotation.*;

/**
 * 设计信息
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("design")
@Api(value = "设计信息", tags = "设计信息接口")
public class DesignController extends BladeController {

	private final IDesignService designService;

	private final IReviewOrderService reviewOrderService;

	/**
	 * 设计信息详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入orderId")
	public R<DesignVO> detail(@RequestParam("orderId") Long orderId) {
		DesignVO detail = designService.designDetail(orderId);
		return R.data(detail);
	}


	/**
	 * 设计信息保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "传入designSubmitDTO")
	public R<Boolean> save(@RequestBody DesignSubmitDTO designSubmitDTO) {
		return R.status(designService.save(designSubmitDTO));
	}

	/**
	 * 设计信息提交
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "提交", notes = "传入designSubmitDTO")
	public R<Boolean> submit(@RequestBody DesignSubmitDTO designSubmitDTO) {
		return R.status(designService.submit(designSubmitDTO));
	}

	/**
	 * 设计信息审核
	 */
	@PostMapping("/audit")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "审核", notes = "传入orderFlowDTO")
	public R<?> audit(@RequestBody OrderFlowDTO orderFlowDTO) {
		return reviewOrderService.examineApprove(orderFlowDTO);
	}

}
