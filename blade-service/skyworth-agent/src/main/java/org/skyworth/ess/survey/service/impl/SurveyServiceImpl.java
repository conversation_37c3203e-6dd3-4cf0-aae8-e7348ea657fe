/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.jetbrains.annotations.Nullable;
import org.skyworth.ess.additionalInfo.service.IAdditionalInfoService;
import org.skyworth.ess.constant.SourceTypeEnum;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.createorder.order.vo.OrderVO;
import org.skyworth.ess.installwoassignment.constant.UserTypeEnum;
import org.skyworth.ess.ordernodesubstatus.service.IOrderNodeSubStatusService;
import org.skyworth.ess.ordernodesubstatus.vo.ModuleFunctionalStatusVO;
import org.skyworth.ess.paymentconfirmation.quoteInfo.entity.QuoteInfoEntity;
import org.skyworth.ess.paymentconfirmation.quoteInfo.service.IQuoteInfoService;
import org.skyworth.ess.revieworder.orderworkflow.service.IAttachmentInfoService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.survey.constant.HouseCategory;
import org.skyworth.ess.survey.constant.HouseModuleType;
import org.skyworth.ess.survey.constant.OperationStatus;
import org.skyworth.ess.survey.constant.OperationType;
import org.skyworth.ess.survey.dto.*;
import org.skyworth.ess.survey.houseelectrical.entity.HouseElectricalEntity;
import org.skyworth.ess.survey.houseelectrical.service.IHouseElectricalService;
import org.skyworth.ess.survey.houseelectrical.vo.HouseElectricalVO;
import org.skyworth.ess.survey.houseelectrical.wrapper.HouseElectricalWrapper;
import org.skyworth.ess.survey.houseelectricalapply.entity.HouseElectricalApplyEntity;
import org.skyworth.ess.survey.houseelectricalapply.service.IHouseElectricalApplyService;
import org.skyworth.ess.survey.houseelectricalapply.vo.HouseElectricalApplyVO;
import org.skyworth.ess.survey.houseelectricalapply.wrapper.HouseElectricalApplyWrapper;
import org.skyworth.ess.survey.housestructure.entity.HouseStructureEntity;
import org.skyworth.ess.survey.housestructure.service.IHouseStructureService;
import org.skyworth.ess.survey.housestructure.vo.HouseStructureVO;
import org.skyworth.ess.survey.housestructure.wrapper.HouseStructureWrapper;
import org.skyworth.ess.survey.info.entity.SurveyInfoEntity;
import org.skyworth.ess.survey.info.service.ISurveyInfoService;
import org.skyworth.ess.survey.service.ISurveyService;
import org.skyworth.ess.survey.vo.*;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.IOrderRelatedUserService;
import org.skyworth.ess.utils.MyFileConvertUtil;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.mail.SendMail;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.SkyWorthFileEntity;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.skyworth.ess.constant.OrderStatusConstants.EXAMINE_APPROVE_PASS;


/**
 * 踏勘服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Service
@AllArgsConstructor
public class SurveyServiceImpl implements ISurveyService {
	private final IHouseStructureService houseStructureService;

	private final IHouseElectricalService houseElectricalService;

	private final IHouseElectricalApplyService houseElectricalApplyService;

	private final ISurveyInfoService infoService;

	private final IOrderNodeSubStatusService orderNodeSubStatusService;

	private final IReviewOrderService reviewOrderService;

	private final IOrderService orderService;

	private final IAttachmentInfoService attachmentInfoService;

	private final IAttachmentInfoClient attachmentInfoClient;

	private final SendMail sendMail;

	private final IQuoteInfoService quoteInfoService;

	private final IAdditionalInfoService additionalInfoService;

	private final IOrderRelatedUserService orderRelatedUserService;

	private final IDictBizClient dictBizClient;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveOrUpdateSurvey(SurveyInfoDTO surveyInfoDTO) {
		// 验证踏勘信息是否已经提交
		checkSurveySubmit(surveyInfoDTO.getOrderId());

		// 保存或者修改踏勘房屋结构信息
		if (surveyInfoDTO.getIdentification().isHouseStructure()) {
			// 验证房屋结构数据
			checkHouseStructure(surveyInfoDTO);

			// 保存或者修改踏勘房屋结构信息
			surveyInfoDTO.getHouseStructure().setOrderId(surveyInfoDTO.getOrderId());
			houseStructureService.saveOrUpdate(surveyInfoDTO.getHouseStructure());

			// 修改踏勘人员拍照与签名信息
			modifySignPhoto(surveyInfoDTO);

			// 保存填写状态标识
			orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.HOUSEINFO.getDec(), surveyInfoDTO.getSaveStatus().getHouseStructure());


		}

		// 保存或者修改踏勘房屋电气信息
		if (surveyInfoDTO.getIdentification().isHouseElectrical()) {
			// 验证踏勘房屋电气信息
			checkHouseElectrical(surveyInfoDTO);

			surveyInfoDTO.getHouseElectrical().setOrderId(surveyInfoDTO.getOrderId());
			houseElectricalService.saveOrUpdate(surveyInfoDTO.getHouseElectrical());

			// 修改电器工程师拍照与签名信息
			modifySignPhoto(surveyInfoDTO);

			// 保存填写状态标识
			orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.HOUSEELECTRICAL.getDec(), surveyInfoDTO.getSaveStatus().getHouseElectrical());

		}

		// 保存踏勘房屋电气应用设备
		if (surveyInfoDTO.getIdentification().isHouseElectricalApply()) {
			LambdaQueryWrapper<HouseElectricalApplyEntity> electricalApplyWrapper = new LambdaQueryWrapper<>();
			electricalApplyWrapper.eq(HouseElectricalApplyEntity::getOrderId, surveyInfoDTO.getOrderId());
			houseElectricalApplyService.remove(electricalApplyWrapper);

			// 批量插入
			if (CollectionUtils.isNotEmpty(surveyInfoDTO.getHouseElectricalApply())) {
				surveyInfoDTO.getHouseElectricalApply().forEach(e -> e.setOrderId(surveyInfoDTO.getOrderId()));
				houseElectricalApplyService.saveBatch(surveyInfoDTO.getHouseElectricalApply());
			}

			// 保存填写状态标识
			orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.HOUSEELECTRICALAPPLY.getDec(), surveyInfoDTO.getSaveStatus().getHouseElectricalApply());

		}
		// 业主签名
		landlordSign(surveyInfoDTO);

		attachmentInfoService.saveAttachmentInfo(surveyInfoDTO);

		return true;
	}

	@Override
	public SurveyInfoVO surveyDetail(SurveyDetailDTO surveyDetailDTO) {
		SurveyInfoVO surveyInfo = new SurveyInfoVO();

		// 获取踏勘基本信息
		if (surveyDetailDTO.isQueryBasicInfo()) {
			setOrderBaseInfo(surveyInfo, surveyDetailDTO);
		}

		// 获取踏勘房屋结构信息
		if (surveyDetailDTO.isQueryHouseStructure()) {
			LambdaQueryWrapper<HouseStructureEntity> houseStructureWrapper = new LambdaQueryWrapper<>();
			houseStructureWrapper.eq(HouseStructureEntity::getOrderId, surveyDetailDTO.getOrderId());
			HouseStructureEntity houseStructure = houseStructureService.getOne(houseStructureWrapper);
			if (houseStructure != null) {
				setHouseStructure(surveyInfo, houseStructure);
			}
		}

		// 获取踏勘房屋电气信息
		if (surveyDetailDTO.isQueryHouseElectrical()) {
			LambdaQueryWrapper<HouseElectricalEntity> houseElectricalWrapper = new LambdaQueryWrapper<>();
			houseElectricalWrapper.eq(HouseElectricalEntity::getOrderId, surveyDetailDTO.getOrderId());
			HouseElectricalEntity houseElectrical = houseElectricalService.getOne(houseElectricalWrapper);
			if (houseElectrical != null) {
				setHouseElectrical(surveyInfo, houseElectrical);
			}
		}

		// 获取踏勘房屋电气应用设备
		if (surveyDetailDTO.isQueryHouseElectricalApply()) {
			LambdaQueryWrapper<HouseElectricalApplyEntity> houseElectricalApplyWrapper = new LambdaQueryWrapper<>();
			houseElectricalApplyWrapper.eq(HouseElectricalApplyEntity::getOrderId, surveyDetailDTO.getOrderId());
			List<HouseElectricalApplyEntity> houseElectricalApply = houseElectricalApplyService.list(houseElectricalApplyWrapper);
			if (CollectionUtils.isNotEmpty(houseElectricalApply)) {
				setHouseElectricalApply(surveyInfo, houseElectricalApply);
			}
		}

		// 业主签名
		if (surveyDetailDTO.isQueryLandlordSign()) {
			setLandlordSign(surveyInfo, surveyDetailDTO);
		}

		return surveyInfo;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean surveySign(SurveySignDTO surveySignDTO) {
		UpdateWrapper<SurveyInfoEntity> infoWrapper = new UpdateWrapper<>();
		infoWrapper.eq("order_id", surveySignDTO.getOrderId());

		SurveyInfoEntity surveyInfoEntity = new SurveyInfoEntity();
		String subNodeName = null;

		// 踏勘人员签名
		if (surveySignDTO.getTechnicianSignImgBizKey() != null) {
			surveyInfoEntity.setTechnicianSignImgBizKey(surveySignDTO.getTechnicianSignImgBizKey());
			subNodeName = HouseModuleType.TECHNICIANSIGNATURE.getDec();
		}

		// 电气人员签名
		if (surveySignDTO.getElectricianOwnerImgBizKey() != null) {
			surveyInfoEntity.setElectricianOwnerImgBizKey(surveySignDTO.getElectricianOwnerImgBizKey());
			subNodeName = HouseModuleType.ELECTRICIANSIGNATURE.getDec();
		}

		// 业主签名
		if (surveySignDTO.getLandlordSignImgBizKey() != null) {
			surveyInfoEntity.setLandlordSignImgBizKey(surveySignDTO.getLandlordSignImgBizKey());
			surveyInfoEntity.setLandlordSignDate(new Date());
			subNodeName = HouseModuleType.LANDLORDSIGN.getDec();
		}

		if (StringUtils.isEmpty(subNodeName)) {
			throw new BusinessException("survey.surveySign.imgBizKey.notNull");
		}

		// 更新业务数据以及保存填写状态标识
		orderNodeSubStatusService.saveOrderNodeSubStatus(surveySignDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), subNodeName, OperationStatus.FINISH.getDec());
		attachmentInfoService.saveAttachmentInfo(surveySignDTO);

		return infoService.update(surveyInfoEntity, infoWrapper);
	}

	@Override
	public SurveySignVO surveySignDetail(String orderId) {
		SurveyInfoEntity surveyInfoEntity = getSurveyInfoEntity(orderId);
		if (surveyInfoEntity == null) {
			throw new BusinessException("survey.notNull");
		}
		SurveySignVO surveySign = new SurveySignVO();
		BeanUtils.copyProperties(surveyInfoEntity, surveySign);
		surveySign.setAttachmentDesc(attachmentInfoService.getAttachmentInfo(surveySign));
		return surveySign;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean ownerPhoto(OwnerPhotoDTO ownerPhotoDTO) {
		UpdateWrapper<SurveyInfoEntity> infoWrapper = new UpdateWrapper<>();
		infoWrapper.eq("order_id", ownerPhotoDTO.getOrderId());

		String subNodeName = null;
		SurveyInfoEntity surveyInfoEntity = new SurveyInfoEntity();
		// 踏勘人员拍照
		if (ownerPhotoDTO.getTechnicianPhotoImgBizKey() != null) {
			subNodeName = HouseModuleType.TECHNICIANOWNERPHOTO.getDec();
			surveyInfoEntity.setTechnicianPhotoImgBizKey(ownerPhotoDTO.getTechnicianPhotoImgBizKey());
		}

		// 电气人员拍照
		if (ownerPhotoDTO.getElectricianOwnerPhotoImgBizKey() != null) {
			subNodeName = HouseModuleType.ELECTRICIANOWNERPHOTO.getDec();
			surveyInfoEntity.setElectricianOwnerPhotoImgBizKey(ownerPhotoDTO.getElectricianOwnerPhotoImgBizKey());
		}

		if (StringUtils.isEmpty(subNodeName)) {
			throw new BusinessException("survey.surveyPhoto.imgBizKey.notNull");
		}

		// 更新业务数据以及保存填写状态标识
		orderNodeSubStatusService.saveOrderNodeSubStatus(ownerPhotoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), subNodeName, OperationStatus.FINISH.getDec());
		attachmentInfoService.saveAttachmentInfo(ownerPhotoDTO);

		return infoService.update(surveyInfoEntity, infoWrapper);
	}

	@Override
	public SurveyPhotoVO ownerPhotoDetail(String orderId) {
		SurveyInfoEntity surveyInfoEntity = getSurveyInfoEntity(orderId);
		if (surveyInfoEntity == null) {
			throw new BusinessException("survey.notNull");
		}
		SurveyPhotoVO surveyPhoto = new SurveyPhotoVO();
		BeanUtils.copyProperties(surveyInfoEntity, surveyPhoto);
		surveyPhoto.setAttachmentDesc(attachmentInfoService.getAttachmentInfo(surveyPhoto));
		return surveyPhoto;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(SurveySubmitDTO surveySubmitDTO, String userType) {
		// 验证踏勘信息是否已经提交
		checkSurveySubmit(surveySubmitDTO.getOrderFlowDTO().getBusinessId());

		// app端处理逻辑
		if (SourceTypeEnum.APP.getDec().equals(userType)) {
			checkFinish(Long.valueOf(surveySubmitDTO.getOrderFlowDTO().getBusinessId()));
		}

		// web端逻辑处理
		if (!SourceTypeEnum.APP.getDec().equals(userType)) {
			submit(surveySubmitDTO);
		}

		// 踏勘提交时间
		UpdateWrapper<SurveyInfoEntity> infoWrapper = new UpdateWrapper<>();
		infoWrapper.eq("order_id", surveySubmitDTO.getOrderFlowDTO().getBusinessId());
		SurveyInfoEntity modifyEntity = new SurveyInfoEntity();
		modifyEntity.setSurveySubmitDate(new Date());
		infoService.update(modifyEntity, infoWrapper);

		return reviewOrderService.examineApprove(surveySubmitDTO.getOrderFlowDTO()).isSuccess();

	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean financeSubmit(OrderFlowDTO orderFlowDTO, SkyWorthFileEntity skyWorthFileEntity) {
		LambdaQueryWrapper<QuoteInfoEntity> quoteInfoEntityWrapper = new LambdaQueryWrapper<>();
		quoteInfoEntityWrapper.eq(QuoteInfoEntity::getOrderId, orderFlowDTO.getBusinessId());
		quoteInfoEntityWrapper.last(" for update");

		Map<String, Object> variables = orderFlowDTO.getVariables();
		String examineApproveType = String.valueOf(variables.get("examineApproveType"));
		if (EXAMINE_APPROVE_PASS.equals(examineApproveType)) {
			QuoteInfoEntity quoteInfoEntity = quoteInfoService.getOne(quoteInfoEntityWrapper);
			if (Objects.isNull(quoteInfoEntity)) {
				throw new BusinessException("email.content.sendMail");
			}
		}

		attachmentInfoService.saveAttachmentInfo(skyWorthFileEntity);

		// 调用审批流接口流转
		return reviewOrderService.examineApprove(orderFlowDTO).isSuccess();
	}

	@Override
	public boolean sendMail(MultipartFile[] multipartFile, SendEmailDTO sendEmailDTO) {
		if (sendEmailDTO.getSendEmail() == 1) {
			if (StringUtils.isEmpty(sendEmailDTO.getToEmail())) {
				throw new BusinessException("email.address.notNull");
			}
			// 验证邮箱格式
			checkEmail(sendEmailDTO.getToEmail(), sendEmailDTO.getBccMail());
			if (StringUtils.isEmpty(sendEmailDTO.getContent())) {
				throw new BusinessException("email.content.notNull");
			}
		}
		if (StringUtils.isNotEmpty(sendEmailDTO.getFileUrl())) {
			// url中的特殊字符进行反转义
			String fileUrl = StringEscapeUtils.unescapeHtml4(sendEmailDTO.getFileUrl());
			// 下载文件转成流
			String fileName = StringUtils.isNotEmpty(sendEmailDTO.getFileName()) ? sendEmailDTO.getFileName() : "quote";
			// 去除后缀
			fileName = fileName.substring(0, fileName.lastIndexOf("."));
			MultipartFile quoteMultipartFile = MyFileConvertUtil.urlToMultipartFile(fileUrl, fileName);
			if (ObjectUtil.isNotEmpty(quoteMultipartFile)) {
				if (ObjectUtil.isEmpty(multipartFile)) {
					multipartFile = ArrayUtils.toArray(quoteMultipartFile);
				} else {
					multipartFile = ArrayUtils.add(multipartFile, quoteMultipartFile);
				}
			}
		}
		QuoteInfoEntity quoteInfoEntity = new QuoteInfoEntity();
		quoteInfoEntity.setId(sendEmailDTO.getId());
		LambdaQueryWrapper<QuoteInfoEntity> quoteInfoEntityWrapper = new LambdaQueryWrapper<>();
		quoteInfoEntityWrapper.eq(QuoteInfoEntity::getOrderId, sendEmailDTO.getBusinessId());
		quoteInfoEntityWrapper.last(" for update");
		QuoteInfoEntity quoteInfo = quoteInfoService.getOne(quoteInfoEntityWrapper);
		if (Objects.nonNull(quoteInfo)) {
			quoteInfoEntity.setId(quoteInfo.getId());
		}
		String content = sendEmailDTO.getContent();
		if (StringUtils.isNotEmpty(content)) {
			// content解码
			content = URLDecoder.decode(content, StandardCharsets.UTF_8);
		} else {
			content = "";
		}
		sendEmailDTO.setContent(content);
		if (sendEmailDTO.getSendEmail() == 1) {
			if (Objects.isNull(quoteInfo) || quoteInfo.getSendEmail() == 0) {
				// 发送邮件
				boolean flg = sendMail.sendEmail(multipartFile, sendEmailDTO.getToEmail(), sendEmailDTO.getContent().replaceAll(System.lineSeparator(), "<br/>"), sendEmailDTO.getBccMail(),"Skyworth PV Quotation");
				if (!flg) {
					throw new BusinessException("email.send.fail");
				}
			}
		}
		// 保存邮件内容信息
		quoteInfoEntity.setOrderId(sendEmailDTO.getBusinessId());
		quoteInfoEntity.setSendEmail(sendEmailDTO.getSendEmail());
		quoteInfoEntity.setToEmail(sendEmailDTO.getToEmail());
		quoteInfoEntity.setContent(sendEmailDTO.getContent());
		quoteInfoEntity.setQuoteDocBizKey(sendEmailDTO.getQuoteDocBizKey());
		quoteInfoEntity.setBccMail(sendEmailDTO.getBccMail());

		quoteInfoService.saveOrUpdate(quoteInfoEntity);
		return true;
	}

	@Override
	public EmailVO emailDetail(String businessId) {
		return getInitEmailContent(businessId, BizConstant.NUMBER_ZERO, "mail_content_quote");
	}

	@Nullable
	private String getString(String businessId, String content, String dicMailTemplate) {
		if (ValidationUtil.isEmpty(content)) {
			OrderEntity orderEntity = orderService.getById(businessId);
			String name = orderEntity.getCustomerName();
			List<DictBiz> dictBizList = dictBizClient.getList(dicMailTemplate).getData();
			if (CollectionUtils.isNotEmpty(dictBizList)) {
				DictBiz dictBiz = dictBizList.get(0);
				content = dictBiz.getAttribute1().replace("{name}", name);
			}
		}
		return content;
	}

	/**
	 * 财务审核邮件推送
	 *
	 * @param multipartFile 文件
	 * @param sendEmailDTO  入参
	 * @return boolean
	 * <AUTHOR>
	 * @since 2024/3/25 11:30
	 **/
	@Override
	public boolean paymentConfirmSendMail(MultipartFile[] multipartFile, PaymentConfirmSendEmailDTO sendEmailDTO) {
		if (BizConstant.NUMBER_ONE.equals(sendEmailDTO.getPaymentConfirmSendMail())) {
			if (StringUtils.isEmpty(sendEmailDTO.getPaymentConfirmMailTo())) {
				throw new BusinessException("email.address.notNull");
			}
			// 验证邮箱格式
			checkEmail(sendEmailDTO.getPaymentConfirmMailTo(), sendEmailDTO.getPaymentConfirmMailBcc());
			if (StringUtils.isEmpty(sendEmailDTO.getPaymentConfirmMailContent())) {
				throw new BusinessException("email.content.notNull");
			}
			// 验证主送人数量不能超过5个
			checkEmailCount(sendEmailDTO.getPaymentConfirmMailTo(), BizConstant.NUMBER_FIVE, "email.sender.limit");
			// 验证密送人数量不能超过5个
			checkEmailCount(sendEmailDTO.getPaymentConfirmMailBcc(), BizConstant.NUMBER_FIVE, "email.recipients.limit");
		}
		QuoteInfoEntity quoteInfoEntity = new QuoteInfoEntity();
		quoteInfoEntity.setId(sendEmailDTO.getId());
		LambdaQueryWrapper<QuoteInfoEntity> quoteInfoEntityWrapper = new LambdaQueryWrapper<>();
		quoteInfoEntityWrapper.eq(QuoteInfoEntity::getOrderId, sendEmailDTO.getOrderId());
		quoteInfoEntityWrapper.last(" for update");
		QuoteInfoEntity quoteInfo = quoteInfoService.getOne(quoteInfoEntityWrapper);
		if (Objects.nonNull(quoteInfo)) {
			quoteInfoEntity.setId(quoteInfo.getId());
		}
		// 数据库发送邮件状态为未发送，但页面传递发送邮件标识为需要发送的场景才发邮件，避免因为提交调用2个接口，事务问题导致重复发邮件
		if (BizConstant.NUMBER_ONE.equals(sendEmailDTO.getPaymentConfirmSendMail()) && BizConstant.NUMBER_ZERO.equals(quoteInfo.getPaymentConfirmSendMail())) {
			// 发送邮件
			boolean flg = sendMail.sendEmail(multipartFile, sendEmailDTO.getPaymentConfirmMailTo(), sendEmailDTO.getPaymentConfirmMailContent().replaceAll(System.lineSeparator(), "<br/>"), sendEmailDTO.getPaymentConfirmMailBcc(),"Skyworth Payment Confirmation");
			if (!flg) {
				throw new BusinessException("email.send.fail");
			}
		}
		// 保存邮件内容信息
		quoteInfoEntity.setOrderId(sendEmailDTO.getOrderId());
		quoteInfoEntity.setPaymentConfirmSendMail(sendEmailDTO.getPaymentConfirmSendMail());
		quoteInfoEntity.setPaymentConfirmMailTo(sendEmailDTO.getPaymentConfirmMailTo());
		quoteInfoEntity.setPaymentConfirmMailContent(sendEmailDTO.getPaymentConfirmMailContent());
		quoteInfoEntity.setPaymentConfirmDocBizKey(sendEmailDTO.getPaymentConfirmDocBizKey());
		quoteInfoEntity.setPaymentConfirmMailBcc(sendEmailDTO.getPaymentConfirmMailBcc());
		quoteInfoService.saveOrUpdate(quoteInfoEntity);
		return true;
	}

	@Override
	public EmailVO paymentEmailDetail(String orderId) {
		return getInitEmailContent(orderId, BizConstant.NUMBER_ONE, "mail_content_payment_confirm");
	}

	/**
	 * 查询页面详情
	 *
	 * @param businessId      主键id
	 * @param type            类型 0: 1:财务审批
	 * @param dicMailTemplate 入参
	 * @return EmailVO
	 * <AUTHOR>
	 * @since 2024/3/26 13:55
	 **/
	private EmailVO getInitEmailContent(String businessId, Integer type, String dicMailTemplate) {
		EmailVO emailVO = new EmailVO();
		LambdaQueryWrapper<QuoteInfoEntity> quoteInfoWrapper = new LambdaQueryWrapper<>();
		quoteInfoWrapper.eq(QuoteInfoEntity::getOrderId, businessId);
		quoteInfoWrapper.orderByDesc(QuoteInfoEntity::getCreateTime).last(" limit 1");
		QuoteInfoEntity quoteInfoEntity = quoteInfoService.getOne(quoteInfoWrapper);
		String content = null;
		// 初始化进入费用清单上传
		if (Objects.isNull(quoteInfoEntity) && BizConstant.NUMBER_ZERO.equals(type)) {
			emailVO.setContent(getString(businessId, content, dicMailTemplate));
			return emailVO;
		}
		// 初始化进入缴费确认
		Long paymentConfirmDocBizKey = quoteInfoEntity.getPaymentConfirmDocBizKey();
		if (paymentConfirmDocBizKey == null && BizConstant.NUMBER_ONE.equals(type)) {
			emailVO.setContent(getString(businessId, content, dicMailTemplate));
			return emailVO;
		}
		List<Long> businessIds = new ArrayList<>();
		if (BizConstant.NUMBER_ZERO.equals(type)) {
			businessIds.add(quoteInfoEntity.getQuoteDocBizKey());
			content = quoteInfoEntity.getContent();
		} else if (BizConstant.NUMBER_ONE.equals(type)) {
			businessIds.add(paymentConfirmDocBizKey);
			content = quoteInfoEntity.getPaymentConfirmMailContent();
		}
		Map<Long, List<AttachmentInfoEntity>> attachmentInfoMap = attachmentInfoClient.findByBusinessIds(businessIds).getData();
		if (!attachmentInfoMap.isEmpty()) {
			emailVO.setAttachmentInfo(attachmentInfoMap.get(businessIds.get(0)));
		}
		emailVO.setId(quoteInfoEntity.getId());
		if (BizConstant.NUMBER_ZERO.equals(type)) {
			emailVO.setSendEmail(quoteInfoEntity.getSendEmail());
			emailVO.setToEmail(quoteInfoEntity.getToEmail());
			emailVO.setBccEmail(quoteInfoEntity.getBccMail());
		} else if (BizConstant.NUMBER_ONE.equals(type)) {
			emailVO.setSendEmail(quoteInfoEntity.getPaymentConfirmSendMail());
			emailVO.setToEmail(quoteInfoEntity.getPaymentConfirmMailTo());
			emailVO.setBccEmail(quoteInfoEntity.getPaymentConfirmMailBcc());
		}
		emailVO.setContent(getString(businessId, content, dicMailTemplate));
		return emailVO;
	}

	/**
	 * 查询踏勘信息
	 *
	 * @param orderId 订单ID
	 * @return 踏勘信息
	 */
	private SurveyInfoEntity getSurveyInfoEntity(Object orderId) {
		LambdaQueryWrapper<SurveyInfoEntity> surveyInfoWrapper = new LambdaQueryWrapper<>();
		surveyInfoWrapper.eq(SurveyInfoEntity::getOrderId, orderId);

		return infoService.getOne(surveyInfoWrapper);

	}

	/**
	 * 设置踏勘人员信息
	 *
	 * @param orderId  订单ID
	 * @param baseInfo 订单基本信息
	 */
	private void setOrderRelatedUser(Object orderId, BaseInfoVO baseInfo) {
		LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserWrapper = new LambdaQueryWrapper<>();
		orderRelatedUserWrapper.eq(OrderRelatedUserEntity::getOrderId, orderId);
		List<OrderRelatedUserEntity> users = orderRelatedUserService.list(orderRelatedUserWrapper);
		if (CollectionUtils.isNotEmpty(users)) {
			users.forEach(user -> {
				if (UserTypeEnum.ELECTRICIAN.getName().equals(user.getUserType())) {
					baseInfo.setElectricianId(user.getUserId());
					baseInfo.setElectricianName(user.getUserName());
				}
				if (UserTypeEnum.SURVEY_USER.getName().equals(user.getUserType())) {
					baseInfo.setTechnicianId(user.getUserId());
					baseInfo.setTechnicianName(user.getUserName());
				}
			});
		}
	}

	/**
	 * 验证房屋数据
	 *
	 * @param surveyInfoDTO surveyInfoDTO
	 */
	private void checkHouseStructure(SurveyInfoDTO surveyInfoDTO) {
		if (surveyInfoDTO.getHouseStructure() == null) {
			throw new BusinessException("survey.submit.houseStructureEmpty");
		}
		if (surveyInfoDTO.getHouseStructure().getId() == null) {
			LambdaQueryWrapper<HouseStructureEntity> houseStructureWrapper = new LambdaQueryWrapper<>();
			houseStructureWrapper.eq(HouseStructureEntity::getOrderId, surveyInfoDTO.getOrderId());
			HouseStructureEntity houseStructure = houseStructureService.getOne(houseStructureWrapper);
			if (houseStructure != null) {
				throw new BusinessException("survey.submit.houseStructureAlreadySaved");
			}
		}
	}

	/**
	 * 验证订单电器数据
	 *
	 * @param surveyInfoDTO surveyInfoDTO
	 */
	private void checkHouseElectrical(SurveyInfoDTO surveyInfoDTO) {
		if (surveyInfoDTO.getHouseElectrical() == null) {
			throw new BusinessException("survey.save.houseElectricalEmpty");
		}
		if (surveyInfoDTO.getHouseElectrical().getId() == null) {
			LambdaQueryWrapper<HouseElectricalEntity> houseElectricalWrapper = new LambdaQueryWrapper<>();
			houseElectricalWrapper.eq(HouseElectricalEntity::getOrderId, surveyInfoDTO.getOrderId());
			HouseElectricalEntity houseElectrical = houseElectricalService.getOne(houseElectricalWrapper);
			if (houseElectrical != null) {
				throw new BusinessException("survey.save.houseElectricalAlreadySaved");
			}
		}
	}

	private void checkSurveySubmit(Object orderId) {
		SurveyInfoEntity surveyInfoEntity = getSurveyInfoEntity(orderId);
		if (surveyInfoEntity == null) {
			throw new BusinessException("survey.notNull");
		}
		if (surveyInfoEntity.getSurveySubmitDate() != null) {
			throw new BusinessException("survey.submit.finish");
		}
	}

	/**
	 * 修改签名与拍照
	 *
	 * @param surveyInfoDTO surveyInfoDTO
	 */
	private void modifySignPhoto(SurveyInfoDTO surveyInfoDTO) {
		UpdateWrapper<SurveyInfoEntity> infoWrapper = new UpdateWrapper<>();
		infoWrapper.eq("order_id", surveyInfoDTO.getOrderId());
		SurveyInfoEntity surveyInfoEntity = new SurveyInfoEntity();

		boolean flg = false;
		if (surveyInfoDTO.getIdentification().isHouseStructure()) {
			if (Objects.nonNull(surveyInfoDTO.getHouseStructure().getTechnicianSignImgBizKey())) {
				surveyInfoEntity.setTechnicianSignImgBizKey(surveyInfoDTO.getHouseStructure().getTechnicianSignImgBizKey());
				orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.TECHNICIANSIGNATURE.getDec(), OperationStatus.FINISH.getDec());
				infoService.update(surveyInfoEntity, infoWrapper);
				flg = true;
			}
			if (Objects.nonNull(surveyInfoDTO.getHouseStructure().getTechnicianPhotoImgBizKey())) {
				surveyInfoEntity.setTechnicianPhotoImgBizKey(surveyInfoDTO.getHouseStructure().getTechnicianPhotoImgBizKey());
				orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.TECHNICIANOWNERPHOTO.getDec(), OperationStatus.FINISH.getDec());
				infoService.update(surveyInfoEntity, infoWrapper);
				flg = true;
			}

		}

		if (surveyInfoDTO.getIdentification().isHouseElectrical()) {
			if (Objects.nonNull(surveyInfoDTO.getHouseElectrical().getElectricianOwnerImgBizKey())) {
				surveyInfoEntity.setElectricianOwnerImgBizKey(surveyInfoDTO.getHouseElectrical().getElectricianOwnerImgBizKey());
				orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.ELECTRICIANSIGNATURE.getDec(), OperationStatus.FINISH.getDec());
				infoService.update(surveyInfoEntity, infoWrapper);
				flg = true;
			}
			if (Objects.nonNull(surveyInfoDTO.getHouseElectrical().getElectricianOwnerPhotoImgBizKey())) {
				surveyInfoEntity.setElectricianOwnerPhotoImgBizKey(surveyInfoDTO.getHouseElectrical().getElectricianOwnerPhotoImgBizKey());
				orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.ELECTRICIANOWNERPHOTO.getDec(), OperationStatus.FINISH.getDec());
				infoService.update(surveyInfoEntity, infoWrapper);
				flg = true;
			}
		}

		if (flg) {
			infoService.update(surveyInfoEntity, infoWrapper);
		}

	}

	private void submitSignPhoto(SurveySubmitDTO surveyInfoDTO) {
		UpdateWrapper<SurveyInfoEntity> infoWrapper = new UpdateWrapper<>();
		infoWrapper.eq("order_id", surveyInfoDTO.getOrderId());
		SurveyInfoEntity surveyInfoEntity = new SurveyInfoEntity();

		if (Objects.nonNull(surveyInfoDTO.getHouseStructure())) {
			if (Objects.nonNull(surveyInfoDTO.getHouseStructure().getTechnicianSignImgBizKey())) {
				surveyInfoEntity.setTechnicianSignImgBizKey(surveyInfoDTO.getHouseStructure().getTechnicianSignImgBizKey());
				orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.TECHNICIANSIGNATURE.getDec(), OperationStatus.FINISH.getDec());
				infoService.update(surveyInfoEntity, infoWrapper);
			}
			if (Objects.nonNull(surveyInfoDTO.getHouseStructure().getTechnicianPhotoImgBizKey())) {
				surveyInfoEntity.setTechnicianPhotoImgBizKey(surveyInfoDTO.getHouseStructure().getTechnicianPhotoImgBizKey());
				orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.TECHNICIANOWNERPHOTO.getDec(), OperationStatus.FINISH.getDec());
				infoService.update(surveyInfoEntity, infoWrapper);
			}
		}

		if (Objects.nonNull(surveyInfoDTO.getHouseElectrical())) {
			if (Objects.nonNull(surveyInfoDTO.getHouseElectrical().getElectricianOwnerImgBizKey())) {
				surveyInfoEntity.setElectricianOwnerImgBizKey(surveyInfoDTO.getHouseElectrical().getElectricianOwnerImgBizKey());
				orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.ELECTRICIANSIGNATURE.getDec(), OperationStatus.FINISH.getDec());
				infoService.update(surveyInfoEntity, infoWrapper);
			}
			if (Objects.nonNull(surveyInfoDTO.getHouseElectrical().getElectricianOwnerPhotoImgBizKey())) {
				surveyInfoEntity.setElectricianOwnerPhotoImgBizKey(surveyInfoDTO.getHouseElectrical().getElectricianOwnerPhotoImgBizKey());
				orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.ELECTRICIANOWNERPHOTO.getDec(), OperationStatus.FINISH.getDec());
				infoService.update(surveyInfoEntity, infoWrapper);
			}
		}

	}

	/**
	 * 业主签名
	 *
	 * @param surveyInfoDTO surveyInfoDTO
	 */
	private void landlordSign(SurveyInfoDTO surveyInfoDTO) {
		UpdateWrapper<SurveyInfoEntity> infoWrapper = new UpdateWrapper<>();
		infoWrapper.eq("order_id", surveyInfoDTO.getOrderId());
		SurveyInfoEntity surveyInfoEntity = new SurveyInfoEntity();

		// 业主签名
		if (StringUtils.isNotEmpty(surveyInfoDTO.getSaveStatus().getLandlordSign())) {
			if (Objects.nonNull(surveyInfoDTO.getLandlordSignImgBizKey()) && OperationStatus.FINISH.getDec().equals(surveyInfoDTO.getSaveStatus().getLandlordSign())) {
				surveyInfoEntity.setLandlordSignDate(new Date());
			}
			orderNodeSubStatusService.saveOrderNodeSubStatus(surveyInfoDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.LANDLORDSIGN.getDec(), surveyInfoDTO.getSaveStatus().getLandlordSign());
			if (surveyInfoDTO.getLandlordSignImgBizKey() != null) {
				surveyInfoEntity.setLandlordSignImgBizKey(surveyInfoDTO.getLandlordSignImgBizKey());
				infoService.update(surveyInfoEntity, infoWrapper);
			}
		}

	}

	/**
	 * 设置订单基本信息
	 *
	 * @param surveyInfo      surveyInfo
	 * @param surveyDetailDTO surveyDetailDTO
	 */
	private void setOrderBaseInfo(SurveyInfoVO surveyInfo, SurveyDetailDTO surveyDetailDTO) {
		BaseInfoVO baseInfo = new BaseInfoVO();
		OrderEntity order = new OrderEntity();
		order.setId(surveyDetailDTO.getOrderId());
		R<?> resultData = orderService.selectBasicOrderInfo(order);
		if (resultData != null && resultData.getData() != null) {
			OrderVO orderVO = (OrderVO) resultData.getData();
			BeanUtils.copyProperties(orderVO.getOrderEntity(), baseInfo);
			if (orderVO.getOrderEntity().getSurveyDateConfirm() != null) {
				baseInfo.setSurveyDate(orderVO.getOrderEntity().getSurveyDateConfirm());
			}
		}
		// 设置人员信息
		setOrderRelatedUser(surveyDetailDTO.getOrderId(), baseInfo);
		surveyInfo.setBaseInfo(baseInfo);
	}


	/**
	 * 设置房屋结构图片与描述信息
	 *
	 * @param surveyInfo     surveyInfo
	 * @param houseStructure houseStructure
	 */
	private void setHouseStructure(SurveyInfoVO surveyInfo, HouseStructureEntity houseStructure) {
		SurveyInfoEntity surveyInfoEntity = getSurveyInfoEntity(houseStructure.getOrderId());
		if (surveyInfoEntity != null) {
			houseStructure.setTechnicianSignImgBizKey(surveyInfoEntity.getTechnicianSignImgBizKey());
			houseStructure.setTechnicianPhotoImgBizKey(surveyInfoEntity.getTechnicianPhotoImgBizKey());
		}

		HouseStructureVO houseStructureVO = HouseStructureWrapper.build().entityVO(houseStructure);
		houseStructureVO.setAttachmentDesc(attachmentInfoService.getAttachmentInfo(houseStructure));
		surveyInfo.setHouseStructure(houseStructureVO);
	}

	/**
	 * 设置踏勘房屋电气图片与图片描述信息
	 *
	 * @param surveyInfo      surveyInfo
	 * @param houseElectrical houseElectrical
	 */
	private void setHouseElectrical(SurveyInfoVO surveyInfo, HouseElectricalEntity houseElectrical) {
		SurveyInfoEntity surveyInfoEntity = getSurveyInfoEntity(houseElectrical.getOrderId());
		if (surveyInfoEntity != null) {
			houseElectrical.setElectricianOwnerImgBizKey(surveyInfoEntity.getElectricianOwnerImgBizKey());
			houseElectrical.setElectricianOwnerPhotoImgBizKey(surveyInfoEntity.getElectricianOwnerPhotoImgBizKey());
		}
		HouseElectricalVO houseElectricalVO = HouseElectricalWrapper.build().entityVO(houseElectrical);
		houseElectricalVO.setAttachmentDesc(attachmentInfoService.getAttachmentInfo(houseElectrical));
		surveyInfo.setHouseElectrical(houseElectricalVO);
	}


	/**
	 * 设置踏勘房屋电气应用设备图片与图片描述信息
	 *
	 * @param surveyInfo           surveyInfo
	 * @param houseElectricalApply houseElectricalApply
	 */
	private void setHouseElectricalApply(SurveyInfoVO surveyInfo, List<HouseElectricalApplyEntity> houseElectricalApply) {
		// 循环获取businessIds
		List<Long> businessIds = new ArrayList<>();
		List<HouseElectricalApplyVO> list = HouseElectricalApplyWrapper.build().listVO(houseElectricalApply);
		list.forEach(e -> {
			businessIds.add(e.getApplyImgBizKey());
		});
		// 一次查出图片与图片备注
		Map<Long, List<AttachmentInfoEntity>> attachmentInfoMap = attachmentInfoClient.findByBusinessIds(businessIds).getData();
		Map<Long, String> attachmentInfoViewMap = additionalInfoService.selectAdditionalMapByBusinessIds(businessIds);

		list.forEach(e -> {
			if (e.getApplyImgBizKey() != null) {
				e.setAttachmentInfoDesc(attachmentInfoMap.get(e.getApplyImgBizKey()));
			}
			if (e.getApplyImgBizKey() != null) {
				e.setAttachmentInfoDescView(attachmentInfoViewMap.get(e.getApplyImgBizKey()));
			}
		});
		surveyInfo.setHouseElectricalApply(list);
	}

	/**
	 * 设置签名信息
	 *
	 * @param surveyInfo      surveyInfo
	 * @param surveyDetailDTO surveyDetailDTO
	 */
	private void setLandlordSign(SurveyInfoVO surveyInfo, SurveyDetailDTO surveyDetailDTO) {
		SurveyInfoEntity surveyInfoEntity = getSurveyInfoEntity(surveyDetailDTO.getOrderId());
		if (surveyInfoEntity != null && Objects.nonNull(surveyInfoEntity.getLandlordSignImgBizKey())) {
			LandlordSignVO LandlordSignVO = new LandlordSignVO();
			LandlordSignVO.setLandlordSignImgBizKey(surveyInfoEntity.getLandlordSignImgBizKey());
			LandlordSignVO.setAttachmentDesc(attachmentInfoService.getAttachmentInfo(LandlordSignVO));
			surveyInfo.setLandlordSign(LandlordSignVO);
		}
	}

	private void checkFinish(Long orderId) {
		Object object = orderNodeSubStatusService.queryOrderNodeSubStatusList(orderId, HouseCategory.SURVEY.getDec());
		JSONObject jsonObject = (JSONObject) object;
		List<ModuleFunctionalStatusVO> list = (List<ModuleFunctionalStatusVO>) jsonObject.get("moduleFunctionalStatus");
		if (CollectionUtils.isEmpty(list)) {
			throw new BusinessException("survey.submit.allEmpty");
		}
		if (Objects.isNull(jsonObject.get("technicianSubmitDate"))) {
			throw new BusinessException("survey.submit.houseInfoEmpty");
		}
		if (Objects.isNull(jsonObject.get("electricianSubmitDate"))) {
			throw new BusinessException("survey.submit.electricianEmpty");
		}

		boolean flg = true;
		for (ModuleFunctionalStatusVO moduleFunctionalStatus : list) {
			if (HouseModuleType.LANDLORDSIGN.getDec().equals(moduleFunctionalStatus.getSubNodeName()) && OperationStatus.FINISH.getDec().equals(moduleFunctionalStatus.getSubStatus())) {
				flg = false;
				break;
			}
		}

		if (flg) {
			throw new BusinessException("survey.submit.LandlordSignEmpty");
		}

	}

	/**
	 * web端踏勘提交逻辑
	 *
	 * @param surveySubmitDTO surveySubmitDTO
	 */
	private void submit(SurveySubmitDTO surveySubmitDTO) {
		if (OperationStatus.UNFINISH.getDec().equals(surveySubmitDTO.getSaveStatus().getHouseStructure())) {
			throw new BusinessException("survey.submit.houseInfoEmpty");
		}
		if (OperationStatus.UNFINISH.getDec().equals(surveySubmitDTO.getSaveStatus().getHouseElectrical())) {
			throw new BusinessException("survey.submit.electricianEmpty");
		}

		if (OperationStatus.UNFINISH.getDec().equals(surveySubmitDTO.getSaveStatus().getHouseElectricalApply())) {
			throw new BusinessException("survey.submit.electricianEmpty");
		}

		if (OperationStatus.UNFINISH.getDec().equals(surveySubmitDTO.getSaveStatus().getLandlordSign())) {
			throw new BusinessException("survey.submit.LandlordSignEmpty");
		}

		// 提交踏勘房屋结构信息
		if (Objects.nonNull(surveySubmitDTO.getHouseStructure())) {
			// 保存或者修改踏勘房屋结构信息
			surveySubmitDTO.getHouseStructure().setOrderId(surveySubmitDTO.getOrderId());
			houseStructureService.saveOrUpdate(surveySubmitDTO.getHouseStructure());

			// 保存填写状态标识
			orderNodeSubStatusService.saveOrderNodeSubStatus(surveySubmitDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.HOUSEINFO.getDec(), OperationStatus.FINISH.getDec());
		}

		// 提交踏勘房屋电气信息
		if (Objects.nonNull(surveySubmitDTO.getHouseElectrical())) {
			surveySubmitDTO.getHouseElectrical().setOrderId(surveySubmitDTO.getOrderId());
			houseElectricalService.saveOrUpdate(surveySubmitDTO.getHouseElectrical());

			// 保存填写状态标识
			orderNodeSubStatusService.saveOrderNodeSubStatus(surveySubmitDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.HOUSEELECTRICAL.getDec(), OperationStatus.FINISH.getDec());
		}

		// 提交房屋电气应用设备
		if (Objects.nonNull(surveySubmitDTO.getHouseElectricalApply())) {
			LambdaQueryWrapper<HouseElectricalApplyEntity> electricalApplyWrapper = new LambdaQueryWrapper<>();
			electricalApplyWrapper.eq(HouseElectricalApplyEntity::getOrderId, surveySubmitDTO.getOrderId());
			houseElectricalApplyService.remove(electricalApplyWrapper);

			// 批量插入
			if (CollectionUtils.isNotEmpty(surveySubmitDTO.getHouseElectricalApply())) {
				surveySubmitDTO.getHouseElectricalApply().forEach(e -> e.setOrderId(surveySubmitDTO.getOrderId()));
				houseElectricalApplyService.saveBatch(surveySubmitDTO.getHouseElectricalApply());
			}

			// 保存填写状态标识
			orderNodeSubStatusService.saveOrderNodeSubStatus(surveySubmitDTO.getOrderId(), OperationType.SAVE.getDec(), HouseCategory.SURVEY.getDec(), HouseModuleType.HOUSEELECTRICALAPPLY.getDec(), OperationStatus.FINISH.getDec());
		}

		// 提交业主签名
		if (Objects.nonNull(surveySubmitDTO.getSaveStatus()) && StringUtils.isNotEmpty(surveySubmitDTO.getSaveStatus().getLandlordSign())) {
			SurveyInfoDTO surveyInfoDTO = new SurveyInfoDTO();
			surveyInfoDTO.setOrderId(surveySubmitDTO.getOrderId());
			surveyInfoDTO.setLandlordSignImgBizKey(surveySubmitDTO.getLandlordSignImgBizKey());

			SaveStatusDTO saveStatus = new SaveStatusDTO();
			saveStatus.setLandlordSign(surveySubmitDTO.getSaveStatus().getLandlordSign());
			surveyInfoDTO.setSaveStatus(saveStatus);
			landlordSign(surveyInfoDTO);
		}

		// 提交踏勘人员与电气工程师签名与拍照
		submitSignPhoto(surveySubmitDTO);

		// 提交附件
		attachmentInfoService.saveAttachmentInfo(surveySubmitDTO);
	}

	public static boolean emailValid(String email) {
		boolean tag = true;
		final String pattern1 = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
		final Pattern pattern = Pattern.compile(pattern1);
		final Matcher mat = pattern.matcher(email);
		if (!mat.find()) {
			tag = false;
		}
		return tag;
	}

	public void checkEmail(String... recipientEmails) {
		StringBuilder stringBuilder = new StringBuilder();
		for (String recipientEmail : recipientEmails) {
			if (StringUtils.isNotEmpty(recipientEmail)) {
				String[] emailArray = recipientEmail.split(",");
				for (String email : emailArray) {
					boolean flg = emailValid(email);
					if (!flg) {
						stringBuilder.append(email).append(",");
					}
				}
			}
		}
		if (!stringBuilder.toString().isEmpty()) {
			throw new BusinessException("email.content.invalid", stringBuilder.toString());
		}
	}

	public void checkEmailCount(String emails, Integer maxCount, String errorCode) {
		String[] emailArray = emails.split(",");
		if (emailArray.length > maxCount) {
			throw new BusinessException(errorCode, maxCount);
		}
	}

}
