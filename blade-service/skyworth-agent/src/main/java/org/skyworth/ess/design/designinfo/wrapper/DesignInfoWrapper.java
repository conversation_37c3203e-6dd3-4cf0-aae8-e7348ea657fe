/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.designinfo.wrapper;

import org.skyworth.ess.design.designinfo.entity.DesignInfoEntity;
import org.skyworth.ess.design.designinfo.vo.DesignInfoVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 设计信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public class DesignInfoWrapper extends BaseEntityWrapper<DesignInfoEntity, DesignInfoVO> {

	public static DesignInfoWrapper build() {
		return new DesignInfoWrapper();
 	}

	@Override
	public DesignInfoVO entityVO(DesignInfoEntity designInfo) {
		DesignInfoVO designInfoVO = Objects.requireNonNull(BeanUtil.copy(designInfo, DesignInfoVO.class));

		//User createUser = UserCache.getUser(designInfo.getCreateUser());
		//User updateUser = UserCache.getUser(designInfo.getUpdateUser());
		//designInfoVO.setCreateUserName(createUser.getName());
		//designInfoVO.setUpdateUserName(updateUser.getName());

		return designInfoVO;
	}


}
