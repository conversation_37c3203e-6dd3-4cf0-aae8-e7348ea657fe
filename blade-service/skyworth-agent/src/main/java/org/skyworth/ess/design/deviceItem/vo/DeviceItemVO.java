/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.deviceItem.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;

import java.util.Date;

/**
 * 设备备料清单 视图实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceItemVO extends DeviceItemEntity {
	private static final long serialVersionUID = 1L;
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date beginDate;
	// 结束时间
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date endDate;
	// 查询开始日期之前数据
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date beforeBeginDate;
	// 出库数量
	private Long outQuantity = 0L;
}
