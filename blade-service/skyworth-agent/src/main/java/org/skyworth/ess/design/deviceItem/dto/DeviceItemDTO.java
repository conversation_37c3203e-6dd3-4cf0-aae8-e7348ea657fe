/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.deviceItem.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;
import org.skyworth.ess.design.vo.ItemMergeVO;
import org.skyworth.ess.revieworder.orderworkflow.vo.AttachmentDescVO;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.SkyWorthFileEntity;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备备料清单 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceItemDTO extends DeviceItemEntity {
	private static final long serialVersionUID = 1L;

	//订单id
	@NotNull(message = "{agent.createOrder.orderId.notNull}")
	private Long orderId;

	//附件信息
	private SkyWorthFileEntity skyWorthFileEntity;

	//审批信息
	private OrderFlowDTO orderFlowDTO;


	//物料签收信息
	private List<DeviceItemEntity> deviceItemEntityList;


	//安装技术经理签名图片key
	private Long installTechnicianSignImgBizKey;


	//仓库管理员签名图片key
	private Long warehouseManagerSignImgBizKey;


	//查询时返回的设备备料清单
	private ItemMergeVO itemMergeVO;

	private AttachmentDescVO attachmentInfo;


	//web端调用标志
	private String webFlag;


}
