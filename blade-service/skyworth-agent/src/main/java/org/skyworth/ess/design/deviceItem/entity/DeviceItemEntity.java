/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.deviceItem.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;

/**
 * 设备备料清单 实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@TableName("design_device_item")
@ApiModel(value = "DeviceItem对象", description = "设备备料清单")
@EqualsAndHashCode(callSuper = true)
public class DeviceItemEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 配料型号编码
	 */
	@ApiModelProperty(value = "配料型号编码")
	private String itemCode;


	@ApiModelProperty(value = "收货确认（1确认收货）")
	private Integer confirmFlag;

	/**
	 * 库存确认
	 */
	@ApiModelProperty(value = "库存确认（1已确认）")
	private Integer stockConfirmFlg;

	/**
	 * 数量
	 */
	@ApiModelProperty(value = "数量")
	private String quantity;
	// 物料基础包：数据字典 agent_item_base_package
	private String itemBasePackage;
	// 物料价格：仓库管理员审核后才写入，后续节点拒绝清空
	private BigDecimal itemPrice;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}
