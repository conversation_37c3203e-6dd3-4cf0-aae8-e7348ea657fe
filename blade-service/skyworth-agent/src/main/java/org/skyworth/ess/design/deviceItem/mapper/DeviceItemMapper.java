/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.deviceItem.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;
import org.skyworth.ess.design.deviceItem.excel.DeviceItemExcel;
import org.skyworth.ess.design.deviceItem.vo.DeviceItemVO;
import org.skyworth.ess.design.vo.ProductVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 设备备料清单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public interface DeviceItemMapper extends BaseMapper<DeviceItemEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceItem
	 * @return
	 */
	List<DeviceItemVO> selectDeviceItemPage(IPage page, DeviceItemVO deviceItem);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<DeviceItemExcel> exportDeviceItem(@Param("ew") Wrapper<DeviceItemEntity> queryWrapper);

	/**
	 *  获取设备列表
	 *
	 * @param orderId 订单ID
	 * @return 设备列表
	 */
	List<ProductVO> queryProductListByOrderId(@Param("orderId") Long orderId);

	List<DeviceItemVO> querySkuOutQuantity(@Param("params")DeviceItemVO query,@Param("list")List<String> skuCodeList);

	List<ProductVO> queryBasePackageProductListByOrderId(@Param("orderId") Long orderId,@Param("itemBasePackage")String itemBasePackage);
	// 更新物料包总价格
	int updateBaseSkuPrice(@Param("orderId") Long orderId,@Param("basePackagePrice") BigDecimal basePackagePrice,@Param("itemFinalTotalPrice")BigDecimal itemFinalTotalPrice);
	// 更新自定义物料包价格
	int batchUpdateSkuInfoPrice(@Param("list")List<DeviceItemEntity> list);
}
