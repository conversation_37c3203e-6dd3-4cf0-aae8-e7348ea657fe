package org.skyworth.ess.survey.constant;

/**
 * 踏勘信息模块枚举类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public enum PersonalRole {
	TECHNICIAN(1, "Technician"),
	ELECTRICIAN(2, "Electrician"),
	LANDLORD(3,"Landlord")
	;

	private Integer role;
	private String dec;

	PersonalRole(Integer role, String dec) {
		this.role = role;
		this.dec = dec;
	}

	public static String getDecByRole(Integer role) {
		for (PersonalRole houseModuleType : PersonalRole.values()) {
			if (houseModuleType.getRole().equals(role)) {
				return houseModuleType.getDec();
			}
		}
		return null;
	}

	public Integer getRole() {
		return role;
	}

	public void setRole(Integer role) {
		this.role = role;
	}

	public String getDec() {
		return dec;
	}

	public void setDec(String dec) {
		this.dec = dec;
	}
}
