package org.skyworth.ess.design.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ItemSeparatePriceVO implements Serializable {
	private static final long serialVersionUID = 1L;
	private String itemCode;
	// 物料订单数据
	private Integer itemOrderQuantity;
	// 物料特殊单价价格
	private BigDecimal itemSeparatePrice;
	// 每个特殊物料总价
	private BigDecimal itemSeparateTotalPrice;
}
