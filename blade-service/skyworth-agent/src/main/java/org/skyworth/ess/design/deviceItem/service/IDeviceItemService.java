/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.deviceItem.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;
import org.skyworth.ess.design.deviceItem.excel.DeviceItemExcel;
import org.skyworth.ess.design.deviceItem.vo.DeviceItemVO;
import org.skyworth.ess.design.dto.StockAuditDTO;
import org.skyworth.ess.design.vo.ItemMergeVO;
import org.springblade.core.mp.base.BaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 设备备料清单 服务类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public interface IDeviceItemService extends BaseService<DeviceItemEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceItem
	 * @return
	 */
	IPage<DeviceItemVO> selectDeviceItemPage(IPage<DeviceItemVO> page, DeviceItemVO deviceItem);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<DeviceItemExcel> exportDeviceItem(Wrapper<DeviceItemEntity> queryWrapper);

	/**
	 * 设备备料清单
	 *
	 * @param orderId
	 * @return
	 */
//	List<ProductVO> queryProductListByOrderId(@Param("orderId") Long orderId);
	// 查询订单 基础包对应的物料信息
//	List<ProductVO> queryProductListByOrderId4BasePackage(@Param("orderId") Long orderId);

	ItemMergeVO queryProductList(Long orderId);

	/**
	 *  库存信息审核
	 *
	 * @param stockAuditDTO
	 * @return
	 */
	boolean audit(StockAuditDTO stockAuditDTO);
	List<DeviceItemVO> querySkuOutQuantity(DeviceItemVO query,List<String> skuCodeList);
	// 更新设计主表物料包价格
	int updateDesignBaseSkuPrice(Long orderId,BigDecimal basePackagePrice,BigDecimal itemFinalTotalPrice);

	// 更新自定义物料包价格
	int batchUpdateSkuInfoPrice(List<DeviceItemEntity> list);
}
