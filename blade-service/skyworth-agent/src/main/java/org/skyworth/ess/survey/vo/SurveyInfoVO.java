package org.skyworth.ess.survey.vo;

import lombok.Data;
import org.skyworth.ess.survey.houseelectrical.vo.HouseElectricalVO;
import org.skyworth.ess.survey.houseelectricalapply.vo.HouseElectricalApplyVO;
import org.skyworth.ess.survey.housestructure.vo.HouseStructureVO;

import java.util.List;

@Data
public class SurveyInfoVO {
	private BaseInfoVO baseInfo;

	private HouseStructureVO houseStructure;

	private HouseElectricalVO HouseElectrical;

	private List<HouseElectricalApplyVO> houseElectricalApply;

	private LandlordSignVO landlordSign;

}
