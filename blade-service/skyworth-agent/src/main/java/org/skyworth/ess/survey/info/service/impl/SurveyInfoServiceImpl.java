/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.info.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.survey.info.entity.SurveyInfoEntity;
import org.skyworth.ess.survey.info.excel.SurveyInfoExcel;
import org.skyworth.ess.survey.info.mapper.SurveyInfoMapper;
import org.skyworth.ess.survey.info.service.ISurveyInfoService;
import org.skyworth.ess.survey.info.vo.SurveyInfoVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 踏勘信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Service
public class SurveyInfoServiceImpl extends BaseServiceImpl<SurveyInfoMapper, SurveyInfoEntity> implements ISurveyInfoService {

	@Override
	public IPage<SurveyInfoVO> selectInfoPage(IPage<SurveyInfoVO> page, SurveyInfoVO info) {
		return page.setRecords(baseMapper.selectInfoPage(page, info));
	}


	@Override
	public List<SurveyInfoExcel> exportInfo(Wrapper<SurveyInfoEntity> queryWrapper) {
		List<SurveyInfoExcel> infoList = baseMapper.exportInfo(queryWrapper);
		//infoList.forEach(info -> {
		//	info.setTypeName(DictCache.getValue(DictEnum.YES_NO, Info.getType()));
		//});
		return infoList;
	}

}
