/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.design.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.design.deviceItem.service.IDeviceItemService;
import org.skyworth.ess.design.dto.StockAuditDTO;
import org.skyworth.ess.design.vo.ItemMergeVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

/**
 * 库存信息
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@RestController
@AllArgsConstructor
@RequestMapping("stock")
@Api(value = "库存信息", tags = "库存相关接口")
public class StockController extends BladeController {

	private final IDeviceItemService deviceItemService;

	/**
	 * 库存信息详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入orderId")
	public R<ItemMergeVO> detail(@RequestParam("orderId") Long orderId) {
		ItemMergeVO itemMergeVO = deviceItemService.queryProductList(orderId);
		return R.data(itemMergeVO);
	}

	/**
	 * 库存信息审核
	 */
	@PostMapping("/audit")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "提交", notes = "传入stockAuditDTO")
	public R<Boolean> audit(@RequestBody StockAuditDTO stockAuditDTO) {
		return R.status(deviceItemService.audit(stockAuditDTO));
	}


}
