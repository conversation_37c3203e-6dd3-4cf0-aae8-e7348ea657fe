package org.skyworth.ess.survey.constant;

/**
 * 模块分类枚举类
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
public enum HouseCategory {
	SURVEY(1, "Survey"),
	DESIGN(2, "Design"),
	CONSTRUCTION(3, "Construction"),
    INSTALLATION_WO_ASSIGNENT(4, "InstallationWOAssignment"),
	MATERIAL_COLLECTION_SIGN(5,"materialCollection"),
	EHS_VERIFICATION(6,"ehsVerification")
	;

	private Integer type;
	private String dec;

	HouseCategory(Integer type, String dec) {
		this.type = type;
		this.dec = dec;
	}

	public static String getDecByType(Integer type) {
		for (HouseCategory houseModuleType : HouseCategory.values()) {
			if (houseModuleType.getType().equals(type)) {
				return houseModuleType.getDec();
			}
		}
		return null;
	}


	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDec() {
		return dec;
	}

	public void setDec(String dec) {
		this.dec = dec;
	}
}
